package com.gt06.protocol.message;

import com.gt06.common.constants.GT06Constants;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;

/**
 * GT06消息工厂类
 * 负责根据协议号创建对应的消息实例
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
public final class MessageFactory {
    
    private MessageFactory() {
        // 工具类，禁止实例化
    }
    
    /**
     * 根据协议号创建消息实例
     * 
     * @param protocolNumber 协议号
     * @return 对应的消息实例
     */
    public static GT06Message createMessage(byte protocolNumber) {
        switch (protocolNumber) {
            case GT06Constants.PROTOCOL_LOGIN:
                return new LoginMessage();
            case GT06Constants.PROTOCOL_LOCATION:
                return new LocationMessage();
            case GT06Constants.PROTOCOL_HEARTBEAT:
                return new HeartbeatMessage();
            case GT06Constants.PROTOCOL_ALARM:
                return new AlarmMessage();
            case GT06Constants.PROTOCOL_COMMAND:
                return new CommandMessage();
            case GT06Constants.PROTOCOL_STRING_INFO:
                return new ResponseMessage();
            default:
                log.warn("Unknown protocol number: 0x{}", String.format("%02X", protocolNumber & 0xFF));
                return new UnknownMessage(protocolNumber);
        }
    }
    
    /**
     * 从ByteBuf解析消息
     * 
     * @param buffer 包含完整消息的ByteBuf
     * @return 解析后的消息实例
     * @throws IllegalArgumentException 如果消息格式无效
     */
    public static GT06Message parseMessage(ByteBuf buffer) {
        if (buffer == null || buffer.readableBytes() < GT06Constants.MIN_PACKET_LENGTH) {
            throw new IllegalArgumentException("Buffer is null or too short");
        }
        
        // 保存当前读取位置
        int originalReaderIndex = buffer.readerIndex();
        
        try {
            // 验证起始位
            if (!verifyStartBits(buffer)) {
                throw new IllegalArgumentException("Invalid start bits");
            }
            
            // 读取包长度
            int packetLength = buffer.getUnsignedByte(buffer.readerIndex() + 2);
            
            // 验证数据包长度
            if (buffer.readableBytes() < packetLength + 5) { // +5 for start(2) + length(1) + stop(2)
                throw new IllegalArgumentException("Incomplete packet");
            }
            
            // 读取协议号
            byte protocolNumber = buffer.getByte(buffer.readerIndex() + 3);
            
            // 验证停止位
            int stopBitsIndex = buffer.readerIndex() + 3 + packetLength;
            if (!verifyStopBits(buffer, stopBitsIndex)) {
                throw new IllegalArgumentException("Invalid stop bits");
            }
            
            // 创建消息实例
            GT06Message message = createMessage(protocolNumber);
            
            // 重置读取位置并解码消息
            buffer.readerIndex(originalReaderIndex);
            message.decode(buffer);
            
            // 验证消息
            if (!message.validate()) {
                log.warn("Message validation failed for protocol 0x{}", 
                    String.format("%02X", protocolNumber & 0xFF));
            }
            
            return message;
            
        } catch (Exception e) {
            // 恢复读取位置
            buffer.readerIndex(originalReaderIndex);
            log.error("Failed to parse message", e);
            throw new IllegalArgumentException("Failed to parse message: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证起始位
     * 
     * @param buffer ByteBuf
     * @return 起始位是否正确
     */
    private static boolean verifyStartBits(ByteBuf buffer) {
        if (buffer.readableBytes() < 2) {
            return false;
        }
        
        byte startBit1 = buffer.getByte(buffer.readerIndex());
        byte startBit2 = buffer.getByte(buffer.readerIndex() + 1);
        
        return startBit1 == GT06Constants.START_BITS[0] && 
               startBit2 == GT06Constants.START_BITS[1];
    }
    
    /**
     * 验证停止位
     * 
     * @param buffer ByteBuf
     * @param stopBitsIndex 停止位的索引位置
     * @return 停止位是否正确
     */
    private static boolean verifyStopBits(ByteBuf buffer, int stopBitsIndex) {
        if (stopBitsIndex + 1 >= buffer.writerIndex()) {
            return false;
        }
        
        byte stopBit1 = buffer.getByte(stopBitsIndex);
        byte stopBit2 = buffer.getByte(stopBitsIndex + 1);
        
        return stopBit1 == GT06Constants.STOP_BITS[0] && 
               stopBit2 == GT06Constants.STOP_BITS[1];
    }
    
    /**
     * 检查ByteBuf是否包含完整的GT06消息
     * 
     * @param buffer ByteBuf
     * @return 如果包含完整消息返回消息长度，否则返回-1
     */
    public static int checkCompleteMessage(ByteBuf buffer) {
        if (buffer == null || buffer.readableBytes() < GT06Constants.MIN_PACKET_LENGTH) {
            return -1;
        }
        
        int readerIndex = buffer.readerIndex();
        
        // 查找起始位
        int startIndex = findStartBits(buffer);
        if (startIndex == -1) {
            return -1;
        }
        
        // 检查是否有足够的字节读取包长度
        if (startIndex + 3 >= buffer.writerIndex()) {
            return -1;
        }
        
        // 读取包长度
        int packetLength = buffer.getUnsignedByte(startIndex + 2);
        
        // 计算完整消息长度：起始位(2) + 包长度(1) + 数据内容(packetLength) + 停止位(2)
        int totalLength = 2 + 1 + packetLength + 2;
        
        // 检查是否有完整的消息
        if (buffer.readableBytes() >= totalLength) {
            // 验证停止位
            int stopBitsIndex = startIndex + 3 + packetLength;
            if (verifyStopBits(buffer, stopBitsIndex)) {
                return totalLength;
            }
        }
        
        return -1;
    }
    
    /**
     * 在ByteBuf中查找起始位
     * 
     * @param buffer ByteBuf
     * @return 起始位的索引，如果未找到返回-1
     */
    private static int findStartBits(ByteBuf buffer) {
        int readerIndex = buffer.readerIndex();
        int writerIndex = buffer.writerIndex();
        
        for (int i = readerIndex; i < writerIndex - 1; i++) {
            if (buffer.getByte(i) == GT06Constants.START_BITS[0] && 
                buffer.getByte(i + 1) == GT06Constants.START_BITS[1]) {
                return i;
            }
        }
        
        return -1;
    }
    
    /**
     * 获取协议号的描述
     * 
     * @param protocolNumber 协议号
     * @return 协议描述
     */
    public static String getProtocolDescription(byte protocolNumber) {
        return switch (protocolNumber) {
            case GT06Constants.PROTOCOL_LOGIN -> "登录包";
            case GT06Constants.PROTOCOL_LOCATION -> "定位数据包";
            case GT06Constants.PROTOCOL_HEARTBEAT -> "心跳包";
            case GT06Constants.PROTOCOL_ALARM -> "报警信息";
            case GT06Constants.PROTOCOL_COMMAND -> "下发指令";
            case GT06Constants.PROTOCOL_STRING_INFO -> "字符串信息";
            case GT06Constants.PROTOCOL_LBS_MULTI -> "LBS多基站";
            case GT06Constants.PROTOCOL_ADDRESS_QUERY -> "查询地址";
            case GT06Constants.PROTOCOL_LBS_WIFI -> "LBS+WIFI";
            case GT06Constants.PROTOCOL_AUDIO -> "录音协议";
            case GT06Constants.PROTOCOL_IMSI -> "IMSI上报";
            case GT06Constants.PROTOCOL_ICCID -> "ICCID上报";
            default -> String.format("未知协议(0x%02X)", protocolNumber & 0xFF);
        };
    }
    
    /**
     * 检查协议号是否为已知的协议
     * 
     * @param protocolNumber 协议号
     * @return 是否为已知协议
     */
    public static boolean isKnownProtocol(byte protocolNumber) {
        return switch (protocolNumber) {
            case GT06Constants.PROTOCOL_LOGIN,
                 GT06Constants.PROTOCOL_LOCATION,
                 GT06Constants.PROTOCOL_HEARTBEAT,
                 GT06Constants.PROTOCOL_ALARM,
                 GT06Constants.PROTOCOL_COMMAND,
                 GT06Constants.PROTOCOL_STRING_INFO,
                 GT06Constants.PROTOCOL_LBS_MULTI,
                 GT06Constants.PROTOCOL_ADDRESS_QUERY,
                 GT06Constants.PROTOCOL_LBS_WIFI,
                 GT06Constants.PROTOCOL_AUDIO,
                 GT06Constants.PROTOCOL_IMSI,
                 GT06Constants.PROTOCOL_ICCID -> true;
            default -> false;
        };
    }
    
    /**
     * 未知消息类型的实现
     */
    private static class UnknownMessage extends GT06Message {
        
        public UnknownMessage(byte protocolNumber) {
            super(protocolNumber);
        }
        
        @Override
        public byte[] encode() {
            // 未知消息类型，返回空数组
            return new byte[0];
        }
        
        @Override
        public void decode(ByteBuf buffer) {
            // 读取包头
            readPacketHeader(buffer);
            
            // 跳过内容
            int contentLength = length - 1 - 2 - 2; // 总长度 - 协议号 - 序列号 - CRC
            if (contentLength > 0) {
                this.content = new byte[contentLength];
                buffer.readBytes(this.content);
            }
            
            // 读取包尾
            readPacketTail(buffer);
        }
        
        @Override
        public boolean validate() {
            return serialNumber >= 0 && serialNumber <= 0xFFFF;
        }
        
        @Override
        public String getMessageDescription() {
            return String.format("Unknown message type: 0x%02X", protocolNumber & 0xFF);
        }
        
        @Override
        public boolean needsResponse() {
            // 未知消息类型，默认不需要响应
            return false;
        }
    }
}
