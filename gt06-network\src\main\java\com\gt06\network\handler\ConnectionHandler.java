package com.gt06.network.handler;

import com.gt06.network.session.SessionManager;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 连接管理处理器
 * 负责管理客户端连接的生命周期
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Component
public class ConnectionHandler extends ChannelInboundHandlerAdapter {
    
    private final SessionManager sessionManager;
    
    /** 连接统计 */
    private static final AtomicLong totalConnections = new AtomicLong(0);
    private static final AtomicLong activeConnections = new AtomicLong(0);
    private static final AtomicLong timeoutConnections = new AtomicLong(0);
    
    public ConnectionHandler(SessionManager sessionManager) {
        this.sessionManager = sessionManager;
    }
    
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        InetSocketAddress remoteAddress = (InetSocketAddress) ctx.channel().remoteAddress();
        String clientIp = remoteAddress.getAddress().getHostAddress();
        int clientPort = remoteAddress.getPort();
        
        // 更新连接统计
        long totalCount = totalConnections.incrementAndGet();
        long activeCount = activeConnections.incrementAndGet();
        
        log.info("New connection established: {}:{} (Total: {}, Active: {})", 
            clientIp, clientPort, totalCount, activeCount);
        
        // 创建会话
        sessionManager.createSession(ctx.channel(), clientIp, clientPort);
        
        // 传递事件到下一个处理器
        super.channelActive(ctx);
    }
    
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        InetSocketAddress remoteAddress = (InetSocketAddress) ctx.channel().remoteAddress();
        String clientInfo = remoteAddress != null ? 
            remoteAddress.getAddress().getHostAddress() + ":" + remoteAddress.getPort() : "unknown";
        
        // 更新连接统计
        long activeCount = activeConnections.decrementAndGet();
        
        log.info("Connection closed: {} (Active: {})", clientInfo, activeCount);
        
        // 移除会话
        sessionManager.removeSession(ctx.channel());
        
        // 传递事件到下一个处理器
        super.channelInactive(ctx);
    }
    
    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent idleEvent = (IdleStateEvent) evt;
            handleIdleEvent(ctx, idleEvent);
        } else {
            super.userEventTriggered(ctx, evt);
        }
    }
    
    /**
     * 处理空闲事件
     * 
     * @param ctx 通道上下文
     * @param idleEvent 空闲事件
     */
    private void handleIdleEvent(ChannelHandlerContext ctx, IdleStateEvent idleEvent) {
        InetSocketAddress remoteAddress = (InetSocketAddress) ctx.channel().remoteAddress();
        String clientInfo = remoteAddress != null ? 
            remoteAddress.getAddress().getHostAddress() + ":" + remoteAddress.getPort() : "unknown";
        
        IdleState state = idleEvent.state();
        
        switch (state) {
            case READER_IDLE:
                log.warn("Read timeout for connection: {}", clientInfo);
                handleReadTimeout(ctx);
                break;
                
            case WRITER_IDLE:
                log.warn("Write timeout for connection: {}", clientInfo);
                handleWriteTimeout(ctx);
                break;
                
            case ALL_IDLE:
                log.warn("Read/Write timeout for connection: {}", clientInfo);
                handleAllTimeout(ctx);
                break;
                
            default:
                log.debug("Unknown idle state: {} for connection: {}", state, clientInfo);
                break;
        }
    }
    
    /**
     * 处理读超时
     * 
     * @param ctx 通道上下文
     */
    private void handleReadTimeout(ChannelHandlerContext ctx) {
        // 读超时通常表示客户端长时间没有发送数据
        // 可以发送心跳请求或关闭连接
        
        // 检查会话状态
        if (sessionManager.isAuthenticated(ctx.channel())) {
            // 已认证的连接，发送心跳请求
            log.info("Sending heartbeat request to authenticated client: {}", 
                ctx.channel().remoteAddress());
            // TODO: 发送心跳请求消息
        } else {
            // 未认证的连接，直接关闭
            log.warn("Closing unauthenticated connection due to read timeout: {}", 
                ctx.channel().remoteAddress());
            closeConnection(ctx, "Read timeout - unauthenticated");
        }
    }
    
    /**
     * 处理写超时
     * 
     * @param ctx 通道上下文
     */
    private void handleWriteTimeout(ChannelHandlerContext ctx) {
        // 写超时通常表示网络拥塞或客户端处理能力不足
        log.warn("Write timeout detected for connection: {}", ctx.channel().remoteAddress());
        
        // 可以尝试清理发送队列或降低发送频率
        // 这里暂时只记录日志
    }
    
    /**
     * 处理读写超时
     * 
     * @param ctx 通道上下文
     */
    private void handleAllTimeout(ChannelHandlerContext ctx) {
        // 读写都超时，通常需要关闭连接
        log.warn("All timeout detected, closing connection: {}", ctx.channel().remoteAddress());
        closeConnection(ctx, "All timeout");
    }
    
    /**
     * 关闭连接
     * 
     * @param ctx 通道上下文
     * @param reason 关闭原因
     */
    private void closeConnection(ChannelHandlerContext ctx, String reason) {
        timeoutConnections.incrementAndGet();
        
        log.info("Closing connection: {} - Reason: {}", ctx.channel().remoteAddress(), reason);
        
        // 关闭通道
        ctx.close();
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        InetSocketAddress remoteAddress = (InetSocketAddress) ctx.channel().remoteAddress();
        String clientInfo = remoteAddress != null ? 
            remoteAddress.getAddress().getHostAddress() + ":" + remoteAddress.getPort() : "unknown";
        
        log.error("Exception caught for connection: {}", clientInfo, cause);
        
        // 关闭连接
        closeConnection(ctx, "Exception: " + cause.getMessage());
    }
    
    /**
     * 获取连接统计信息
     * 
     * @return 连接统计
     */
    public static ConnectionStats getConnectionStats() {
        return new ConnectionStats(
            totalConnections.get(),
            activeConnections.get(),
            timeoutConnections.get()
        );
    }
    
    /**
     * 重置连接统计
     */
    public static void resetConnectionStats() {
        totalConnections.set(0);
        activeConnections.set(0);
        timeoutConnections.set(0);
    }
    
    /**
     * 连接统计信息
     */
    public static class ConnectionStats {
        private final long totalConnections;
        private final long activeConnections;
        private final long timeoutConnections;
        
        public ConnectionStats(long totalConnections, long activeConnections, long timeoutConnections) {
            this.totalConnections = totalConnections;
            this.activeConnections = activeConnections;
            this.timeoutConnections = timeoutConnections;
        }
        
        public long getTotalConnections() { return totalConnections; }
        public long getActiveConnections() { return activeConnections; }
        public long getTimeoutConnections() { return timeoutConnections; }
        
        public double getTimeoutRate() {
            return totalConnections > 0 ? (double) timeoutConnections / totalConnections : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format("ConnectionStats{total=%d, active=%d, timeout=%d, timeoutRate=%.2f%%}", 
                totalConnections, activeConnections, timeoutConnections, getTimeoutRate() * 100);
        }
    }
}
