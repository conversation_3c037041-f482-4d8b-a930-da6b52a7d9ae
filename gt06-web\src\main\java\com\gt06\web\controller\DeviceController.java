package com.gt06.web.controller;

import com.gt06.service.entity.Device;
import com.gt06.service.service.DeviceService;
import com.gt06.web.dto.ApiResponse;
import com.gt06.web.dto.DeviceDTO;
import com.gt06.web.dto.DeviceStatsDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Optional;

/**
 * 设备管理控制器
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/devices")
@RequiredArgsConstructor
@Tag(name = "设备管理", description = "设备管理相关接口")
public class DeviceController {
    
    private final DeviceService deviceService;
    
    /**
     * 分页查询设备列表
     */
    @GetMapping
    @Operation(summary = "分页查询设备列表", description = "支持多条件筛选的设备分页查询")
    public ResponseEntity<ApiResponse<Page<DeviceDTO>>> getDevices(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "设备状态") @RequestParam(required = false) Device.DeviceStatus status,
            @Parameter(description = "在线状态") @RequestParam(required = false) Boolean online,
            @Parameter(description = "设备类型") @RequestParam(required = false) String deviceType,
            @Parameter(description = "所有者") @RequestParam(required = false) String owner) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Device> devices = deviceService.findByConditions(status, online, deviceType, owner, pageable);
        Page<DeviceDTO> deviceDTOs = devices.map(this::convertToDTO);
        
        return ResponseEntity.ok(ApiResponse.success(deviceDTOs));
    }
    
    /**
     * 根据IMEI查询设备详情
     */
    @GetMapping("/{imei}")
    @Operation(summary = "查询设备详情", description = "根据IMEI查询设备详细信息")
    public ResponseEntity<ApiResponse<DeviceDTO>> getDevice(
            @Parameter(description = "设备IMEI") @PathVariable String imei) {
        
        Optional<Device> device = deviceService.findByImei(imei);
        if (!device.isPresent()) {
            return ResponseEntity.ok(ApiResponse.error("设备不存在"));
        }
        
        DeviceDTO deviceDTO = convertToDTO(device.get());
        return ResponseEntity.ok(ApiResponse.success(deviceDTO));
    }
    
    /**
     * 创建设备
     */
    @PostMapping
    @Operation(summary = "创建设备", description = "创建新的设备记录")
    public ResponseEntity<ApiResponse<DeviceDTO>> createDevice(
            @Valid @RequestBody DeviceDTO deviceDTO) {
        
        // 检查IMEI是否已存在
        if (deviceService.existsByImei(deviceDTO.getImei())) {
            return ResponseEntity.ok(ApiResponse.error("IMEI已存在"));
        }
        
        // 检查车牌号是否已存在
        if (deviceDTO.getPlateNumber() != null && 
            deviceService.existsByPlateNumber(deviceDTO.getPlateNumber())) {
            return ResponseEntity.ok(ApiResponse.error("车牌号已存在"));
        }
        
        Device device = convertToEntity(deviceDTO);
        Device saved = deviceService.save(device);
        DeviceDTO result = convertToDTO(saved);
        
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 更新设备信息
     */
    @PutMapping("/{imei}")
    @Operation(summary = "更新设备信息", description = "更新指定IMEI的设备信息")
    public ResponseEntity<ApiResponse<DeviceDTO>> updateDevice(
            @Parameter(description = "设备IMEI") @PathVariable String imei,
            @Valid @RequestBody DeviceDTO deviceDTO) {
        
        Optional<Device> existingDevice = deviceService.findByImei(imei);
        if (!existingDevice.isPresent()) {
            return ResponseEntity.ok(ApiResponse.error("设备不存在"));
        }
        
        Device device = existingDevice.get();
        updateDeviceFromDTO(device, deviceDTO);
        Device saved = deviceService.save(device);
        DeviceDTO result = convertToDTO(saved);
        
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 删除设备
     */
    @DeleteMapping("/{imei}")
    @Operation(summary = "删除设备", description = "删除指定IMEI的设备")
    public ResponseEntity<ApiResponse<Void>> deleteDevice(
            @Parameter(description = "设备IMEI") @PathVariable String imei) {
        
        if (!deviceService.existsByImei(imei)) {
            return ResponseEntity.ok(ApiResponse.error("设备不存在"));
        }
        
        deviceService.deleteDevice(imei);
        return ResponseEntity.ok(ApiResponse.success(null));
    }
    
    /**
     * 启用设备
     */
    @PostMapping("/{imei}/enable")
    @Operation(summary = "启用设备", description = "启用指定IMEI的设备")
    public ResponseEntity<ApiResponse<Void>> enableDevice(
            @Parameter(description = "设备IMEI") @PathVariable String imei) {
        
        if (!deviceService.existsByImei(imei)) {
            return ResponseEntity.ok(ApiResponse.error("设备不存在"));
        }
        
        deviceService.enableDevice(imei);
        return ResponseEntity.ok(ApiResponse.success(null));
    }
    
    /**
     * 禁用设备
     */
    @PostMapping("/{imei}/disable")
    @Operation(summary = "禁用设备", description = "禁用指定IMEI的设备")
    public ResponseEntity<ApiResponse<Void>> disableDevice(
            @Parameter(description = "设备IMEI") @PathVariable String imei) {
        
        if (!deviceService.existsByImei(imei)) {
            return ResponseEntity.ok(ApiResponse.error("设备不存在"));
        }
        
        deviceService.disableDevice(imei);
        return ResponseEntity.ok(ApiResponse.success(null));
    }
    
    /**
     * 搜索设备
     */
    @GetMapping("/search")
    @Operation(summary = "搜索设备", description = "根据关键字搜索设备")
    public ResponseEntity<ApiResponse<Page<DeviceDTO>>> searchDevices(
            @Parameter(description = "搜索关键字") @RequestParam String keyword,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Device> devices = deviceService.searchDevices(keyword, pageable);
        Page<DeviceDTO> deviceDTOs = devices.map(this::convertToDTO);
        
        return ResponseEntity.ok(ApiResponse.success(deviceDTOs));
    }
    
    /**
     * 获取设备统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取设备统计信息", description = "获取设备总体统计数据")
    public ResponseEntity<ApiResponse<DeviceStatsDTO>> getDeviceStats() {
        DeviceStatsDTO stats = new DeviceStatsDTO();
        
        stats.setOnlineCount(deviceService.getOnlineDeviceCount());
        stats.setOfflineCount(deviceService.getOfflineDeviceCount());
        stats.setStatusDistribution(deviceService.getStatusDistribution());
        stats.setTypeDistribution(deviceService.getDeviceTypeDistribution());
        
        return ResponseEntity.ok(ApiResponse.success(stats));
    }
    
    /**
     * 获取设备注册趋势
     */
    @GetMapping("/stats/registration-trend")
    @Operation(summary = "获取设备注册趋势", description = "获取指定天数内的设备注册趋势")
    public ResponseEntity<ApiResponse<List<Object[]>>> getRegistrationTrend(
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        List<Object[]> trend = deviceService.getRegistrationTrend(days);
        return ResponseEntity.ok(ApiResponse.success(trend));
    }
    
    /**
     * 获取即将到期的设备
     */
    @GetMapping("/expiring")
    @Operation(summary = "获取即将到期的设备", description = "获取服务即将到期的设备列表")
    public ResponseEntity<ApiResponse<List<DeviceDTO>>> getExpiringDevices(
            @Parameter(description = "提前天数") @RequestParam(defaultValue = "30") int days) {
        
        List<Device> devices = deviceService.findDevicesExpiringSoon(days);
        List<DeviceDTO> deviceDTOs = devices.stream().map(this::convertToDTO).collect(Collectors.toList());
        
        return ResponseEntity.ok(ApiResponse.success(deviceDTOs));
    }
    
    /**
     * 获取长时间离线的设备
     */
    @GetMapping("/offline")
    @Operation(summary = "获取长时间离线的设备", description = "获取长时间离线的设备列表")
    public ResponseEntity<ApiResponse<List<DeviceDTO>>> getLongTimeOfflineDevices(
            @Parameter(description = "离线小时数") @RequestParam(defaultValue = "24") int hours) {
        
        List<Device> devices = deviceService.findLongTimeOfflineDevices(hours);
        List<DeviceDTO> deviceDTOs = devices.stream().map(this::convertToDTO).collect(Collectors.toList());
        
        return ResponseEntity.ok(ApiResponse.success(deviceDTOs));
    }
    
    /**
     * 转换为DTO
     */
    private DeviceDTO convertToDTO(Device device) {
        DeviceDTO dto = new DeviceDTO();
        dto.setId(device.getId());
        dto.setImei(device.getImei());
        dto.setDeviceName(device.getDeviceName());
        dto.setDeviceType(device.getDeviceType());
        dto.setDeviceModel(device.getDeviceModel());
        dto.setSoftwareVersion(device.getSoftwareVersion());
        dto.setHardwareVersion(device.getHardwareVersion());
        dto.setStatus(device.getStatus());
        dto.setOnline(device.getOnline());
        dto.setLastOnlineTime(device.getLastOnlineTime());
        dto.setLastOfflineTime(device.getLastOfflineTime());
        dto.setRegisterTime(device.getRegisterTime());
        dto.setSimNumber(device.getSimNumber());
        dto.setOwner(device.getOwner());
        dto.setContactPhone(device.getContactPhone());
        dto.setPlateNumber(device.getPlateNumber());
        dto.setVehicleType(device.getVehicleType());
        dto.setVehicleBrand(device.getVehicleBrand());
        dto.setVehicleColor(device.getVehicleColor());
        dto.setInstallLocation(device.getInstallLocation());
        dto.setInstallTime(device.getInstallTime());
        dto.setServiceExpireTime(device.getServiceExpireTime());
        dto.setEnabled(device.getEnabled());
        dto.setRemarks(device.getRemarks());
        dto.setCreateTime(device.getCreateTime());
        dto.setUpdateTime(device.getUpdateTime());
        return dto;
    }
    
    /**
     * 转换为实体
     */
    private Device convertToEntity(DeviceDTO dto) {
        Device device = new Device();
        device.setImei(dto.getImei());
        device.setDeviceName(dto.getDeviceName());
        device.setDeviceType(dto.getDeviceType());
        device.setDeviceModel(dto.getDeviceModel());
        device.setSoftwareVersion(dto.getSoftwareVersion());
        device.setHardwareVersion(dto.getHardwareVersion());
        device.setSimNumber(dto.getSimNumber());
        device.setOwner(dto.getOwner());
        device.setContactPhone(dto.getContactPhone());
        device.setPlateNumber(dto.getPlateNumber());
        device.setVehicleType(dto.getVehicleType());
        device.setVehicleBrand(dto.getVehicleBrand());
        device.setVehicleColor(dto.getVehicleColor());
        device.setInstallLocation(dto.getInstallLocation());
        device.setInstallTime(dto.getInstallTime());
        device.setServiceExpireTime(dto.getServiceExpireTime());
        device.setRemarks(dto.getRemarks());
        return device;
    }
    
    /**
     * 从DTO更新实体
     */
    private void updateDeviceFromDTO(Device device, DeviceDTO dto) {
        if (dto.getDeviceName() != null) device.setDeviceName(dto.getDeviceName());
        if (dto.getDeviceType() != null) device.setDeviceType(dto.getDeviceType());
        if (dto.getDeviceModel() != null) device.setDeviceModel(dto.getDeviceModel());
        if (dto.getSoftwareVersion() != null) device.setSoftwareVersion(dto.getSoftwareVersion());
        if (dto.getHardwareVersion() != null) device.setHardwareVersion(dto.getHardwareVersion());
        if (dto.getSimNumber() != null) device.setSimNumber(dto.getSimNumber());
        if (dto.getOwner() != null) device.setOwner(dto.getOwner());
        if (dto.getContactPhone() != null) device.setContactPhone(dto.getContactPhone());
        if (dto.getPlateNumber() != null) device.setPlateNumber(dto.getPlateNumber());
        if (dto.getVehicleType() != null) device.setVehicleType(dto.getVehicleType());
        if (dto.getVehicleBrand() != null) device.setVehicleBrand(dto.getVehicleBrand());
        if (dto.getVehicleColor() != null) device.setVehicleColor(dto.getVehicleColor());
        if (dto.getInstallLocation() != null) device.setInstallLocation(dto.getInstallLocation());
        if (dto.getInstallTime() != null) device.setInstallTime(dto.getInstallTime());
        if (dto.getServiceExpireTime() != null) device.setServiceExpireTime(dto.getServiceExpireTime());
        if (dto.getRemarks() != null) device.setRemarks(dto.getRemarks());
    }
}
