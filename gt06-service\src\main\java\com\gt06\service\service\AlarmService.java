package com.gt06.service.service;

import com.gt06.service.entity.AlarmData;
import com.gt06.service.repository.AlarmDataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 报警处理服务
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlarmService {
    
    private final AlarmDataRepository alarmDataRepository;
    
    /**
     * 保存报警数据
     * 
     * @param alarmData 报警数据
     * @return 保存后的报警数据
     */
    @Transactional
    public AlarmData save(AlarmData alarmData) {
        // 设置默认值
        if (alarmData.getAlarmLevel() == null) {
            alarmData.setAlarmLevel(alarmData.getAlarmType().getDefaultLevel());
        }
        
        if (alarmData.getStatus() == null) {
            alarmData.setStatus(AlarmData.AlarmStatus.PENDING);
        }
        
        AlarmData saved = alarmDataRepository.save(alarmData);
        
        log.info("Alarm saved: IMEI={}, Type={}, Level={}, ID={}", 
            alarmData.getImei(), alarmData.getAlarmType(), alarmData.getAlarmLevel(), saved.getId());
        
        // 处理紧急报警
        if (saved.isCritical()) {
            handleCriticalAlarm(saved);
        }
        
        return saved;
    }
    
    /**
     * 根据ID查找报警
     * 
     * @param id 报警ID
     * @return 报警数据
     */
    public Optional<AlarmData> findById(Long id) {
        return alarmDataRepository.findById(id);
    }
    
    /**
     * 根据IMEI分页查询报警记录
     * 
     * @param imei 设备IMEI
     * @param pageable 分页参数
     * @return 报警记录分页列表
     */
    public Page<AlarmData> findByImei(String imei, Pageable pageable) {
        return alarmDataRepository.findByImeiOrderByAlarmTimeDesc(imei, pageable);
    }
    
    /**
     * 根据IMEI和时间范围查询报警记录
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报警记录列表
     */
    public List<AlarmData> findByImeiAndTimeRange(String imei, LocalDateTime startTime, LocalDateTime endTime) {
        return alarmDataRepository.findByImeiAndAlarmTimeBetweenOrderByAlarmTimeDesc(imei, startTime, endTime);
    }
    
    /**
     * 根据条件查询报警记录
     * 
     * @param imei 设备IMEI
     * @param alarmType 报警类型
     * @param alarmLevel 报警级别
     * @param status 处理状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 报警记录分页列表
     */
    public Page<AlarmData> findByConditions(String imei, AlarmData.AlarmType alarmType,
                                           AlarmData.AlarmLevel alarmLevel, AlarmData.AlarmStatus status,
                                           LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        return alarmDataRepository.findAlarmsByConditions(imei, alarmType, alarmLevel, status, 
                                                         startTime, endTime, pageable);
    }
    
    /**
     * 查找待处理的报警
     * 
     * @param pageable 分页参数
     * @return 待处理报警分页列表
     */
    public Page<AlarmData> findPendingAlarms(Pageable pageable) {
        return alarmDataRepository.findPendingAlarms(pageable);
    }
    
    /**
     * 查找紧急报警
     * 
     * @param pageable 分页参数
     * @return 紧急报警分页列表
     */
    public Page<AlarmData> findCriticalAlarms(Pageable pageable) {
        return alarmDataRepository.findCriticalAlarms(pageable);
    }
    
    /**
     * 查找未通知的报警
     * 
     * @return 未通知报警列表
     */
    public List<AlarmData> findUnnotifiedAlarms() {
        return alarmDataRepository.findByNotifiedFalseOrderByAlarmTimeDesc();
    }
    
    /**
     * 处理报警
     * 
     * @param alarmId 报警ID
     * @param handler 处理人
     * @param remarks 处理备注
     * @return 是否处理成功
     */
    @Transactional
    public boolean handleAlarm(Long alarmId, String handler, String remarks) {
        Optional<AlarmData> alarmOpt = findById(alarmId);
        if (alarmOpt.isEmpty()) {
            log.warn("Alarm not found: ID={}", alarmId);
            return false;
        }
        
        AlarmData alarm = alarmOpt.get();
        alarm.handle(handler, remarks);
        alarmDataRepository.save(alarm);
        
        log.info("Alarm handled: ID={}, Handler={}", alarmId, handler);
        return true;
    }
    
    /**
     * 解决报警
     * 
     * @param alarmId 报警ID
     * @param handler 处理人
     * @param remarks 处理备注
     * @return 是否解决成功
     */
    @Transactional
    public boolean resolveAlarm(Long alarmId, String handler, String remarks) {
        Optional<AlarmData> alarmOpt = findById(alarmId);
        if (alarmOpt.isEmpty()) {
            log.warn("Alarm not found: ID={}", alarmId);
            return false;
        }
        
        AlarmData alarm = alarmOpt.get();
        alarm.resolve(handler, remarks);
        alarmDataRepository.save(alarm);
        
        log.info("Alarm resolved: ID={}, Handler={}", alarmId, handler);
        return true;
    }
    
    /**
     * 忽略报警
     * 
     * @param alarmId 报警ID
     * @param handler 处理人
     * @param remarks 处理备注
     * @return 是否忽略成功
     */
    @Transactional
    public boolean ignoreAlarm(Long alarmId, String handler, String remarks) {
        Optional<AlarmData> alarmOpt = findById(alarmId);
        if (alarmOpt.isEmpty()) {
            log.warn("Alarm not found: ID={}", alarmId);
            return false;
        }
        
        AlarmData alarm = alarmOpt.get();
        alarm.ignore(handler, remarks);
        alarmDataRepository.save(alarm);
        
        log.info("Alarm ignored: ID={}, Handler={}", alarmId, handler);
        return true;
    }
    
    /**
     * 升级报警
     * 
     * @param alarmId 报警ID
     * @param handler 处理人
     * @param remarks 处理备注
     * @return 是否升级成功
     */
    @Transactional
    public boolean escalateAlarm(Long alarmId, String handler, String remarks) {
        Optional<AlarmData> alarmOpt = findById(alarmId);
        if (alarmOpt.isEmpty()) {
            log.warn("Alarm not found: ID={}", alarmId);
            return false;
        }
        
        AlarmData alarm = alarmOpt.get();
        alarm.escalate(handler, remarks);
        alarmDataRepository.save(alarm);
        
        log.warn("Alarm escalated: ID={}, Handler={}, NewLevel={}", 
            alarmId, handler, alarm.getAlarmLevel());
        
        // 升级后的报警需要重新处理紧急流程
        if (alarm.isCritical()) {
            handleCriticalAlarm(alarm);
        }
        
        return true;
    }
    
    /**
     * 批量处理报警
     * 
     * @param alarmIds 报警ID列表
     * @param status 新状态
     * @param handler 处理人
     * @param remarks 处理备注
     * @return 处理数量
     */
    @Transactional
    public int batchHandleAlarms(List<Long> alarmIds, AlarmData.AlarmStatus status, 
                                String handler, String remarks) {
        int updated = alarmDataRepository.batchUpdateStatus(alarmIds, status, handler, 
                                                           LocalDateTime.now(), remarks);
        log.info("Batch handled {} alarms with status: {}", updated, status);
        return updated;
    }
    
    /**
     * 标记报警已通知
     * 
     * @param alarmId 报警ID
     * @param notifyMethod 通知方式
     * @return 是否标记成功
     */
    @Transactional
    public boolean markAlarmNotified(Long alarmId, String notifyMethod) {
        Optional<AlarmData> alarmOpt = findById(alarmId);
        if (alarmOpt.isEmpty()) {
            return false;
        }
        
        AlarmData alarm = alarmOpt.get();
        alarm.markNotified(notifyMethod);
        alarmDataRepository.save(alarm);
        
        log.debug("Alarm marked as notified: ID={}, Method={}", alarmId, notifyMethod);
        return true;
    }
    
    /**
     * 批量标记已通知
     * 
     * @param alarmIds 报警ID列表
     * @param notifyMethod 通知方式
     * @return 标记数量
     */
    @Transactional
    public int batchMarkNotified(List<Long> alarmIds, String notifyMethod) {
        int updated = alarmDataRepository.batchMarkNotified(alarmIds, LocalDateTime.now(), notifyMethod);
        log.debug("Batch marked {} alarms as notified", updated);
        return updated;
    }
    
    /**
     * 获取待处理报警数量
     * 
     * @return 待处理报警数量
     */
    @Cacheable(value = "alarm:stats", key = "'pending_count'")
    public long getPendingAlarmCount() {
        return alarmDataRepository.countByStatus(AlarmData.AlarmStatus.PENDING);
    }
    
    /**
     * 获取紧急报警数量
     * 
     * @return 紧急报警数量
     */
    @Cacheable(value = "alarm:stats", key = "'critical_count'")
    public long getCriticalAlarmCount() {
        return alarmDataRepository.countByAlarmLevel(AlarmData.AlarmLevel.CRITICAL);
    }
    
    /**
     * 统计报警类型分布
     * 
     * @param days 天数
     * @return 报警类型分布统计
     */
    public List<Object[]> getAlarmTypeDistribution(int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        LocalDateTime endTime = LocalDateTime.now();
        return alarmDataRepository.getAlarmTypeDistribution(startTime, endTime);
    }
    
    /**
     * 统计报警级别分布
     * 
     * @param days 天数
     * @return 报警级别分布统计
     */
    public List<Object[]> getAlarmLevelDistribution(int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        LocalDateTime endTime = LocalDateTime.now();
        return alarmDataRepository.getAlarmLevelDistribution(startTime, endTime);
    }
    
    /**
     * 获取每日报警趋势
     * 
     * @param days 天数
     * @return 每日报警趋势统计
     */
    public List<Object[]> getDailyAlarmTrend(int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        return alarmDataRepository.getDailyAlarmTrend(startTime);
    }
    
    /**
     * 获取设备报警频率统计
     * 
     * @param days 天数
     * @return 设备报警频率统计
     */
    public List<Object[]> getDeviceAlarmFrequency(int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        LocalDateTime endTime = LocalDateTime.now();
        return alarmDataRepository.getDeviceAlarmFrequency(startTime, endTime);
    }
    
    /**
     * 获取最近报警记录
     * 
     * @param limit 限制数量
     * @return 最近报警记录列表
     */
    public List<AlarmData> getRecentAlarms(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return alarmDataRepository.findRecentAlarms(pageable);
    }
    
    /**
     * 查找超时未处理的报警
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 超时报警列表
     */
    public List<AlarmData> findTimeoutAlarms(int timeoutMinutes) {
        LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);
        return alarmDataRepository.findTimeoutAlarms(timeoutTime);
    }
    
    /**
     * 计算平均处理时间
     * 
     * @param days 天数
     * @return 平均处理时间（分钟）
     */
    public Double getAverageHandleTime(int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        LocalDateTime endTime = LocalDateTime.now();
        Double avgTime = alarmDataRepository.getAverageHandleTime(startTime, endTime);
        return avgTime != null ? avgTime : 0.0;
    }
    
    /**
     * 删除过期的报警数据
     * 
     * @param retentionDays 保留天数
     * @return 删除数量
     */
    @Transactional
    public int deleteExpiredAlarms(int retentionDays) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(retentionDays);
        int deleted = alarmDataRepository.deleteExpiredAlarms(expireTime);
        log.info("Deleted {} expired alarm records older than {} days", deleted, retentionDays);
        return deleted;
    }
    
    /**
     * 处理紧急报警
     * 
     * @param alarm 报警数据
     */
    private void handleCriticalAlarm(AlarmData alarm) {
        log.error("CRITICAL ALARM DETECTED: ID={}, IMEI={}, Type={}, Location={}", 
            alarm.getId(), alarm.getImei(), alarm.getAlarmType().getDescription(), 
            alarm.getLocationDescription());
        
        // TODO: 实现紧急报警处理逻辑
        // 1. 发送实时通知（短信、邮件、推送）
        // 2. 记录到紧急事件日志
        // 3. 触发自动响应流程
        // 4. 通知相关负责人
        // 5. 启动应急预案
        
        // 示例：异步发送通知
        // notificationService.sendCriticalAlarmNotification(alarm);
        
        // 示例：记录紧急事件
        // emergencyEventService.recordCriticalEvent(alarm);
    }
    
    /**
     * 创建报警数据
     * 
     * @param imei 设备IMEI
     * @param alarmType 报警类型
     * @param alarmTime 报警时间
     * @param latitude 纬度
     * @param longitude 经度
     * @param address 地址
     * @param rawData 原始数据
     * @return 创建的报警数据
     */
    public AlarmData createAlarm(String imei, AlarmData.AlarmType alarmType, LocalDateTime alarmTime,
                                Double latitude, Double longitude, String address, String rawData) {
        AlarmData alarm = new AlarmData();
        alarm.setImei(imei);
        alarm.setAlarmType(alarmType);
        alarm.setAlarmLevel(alarmType.getDefaultLevel());
        alarm.setAlarmTime(alarmTime);
        alarm.setAlarmDescription(alarmType.getDescription());
        
        if (latitude != null && longitude != null) {
            alarm.setLatitude(java.math.BigDecimal.valueOf(latitude));
            alarm.setLongitude(java.math.BigDecimal.valueOf(longitude));
            alarm.setGpsFixed(true);
        }
        
        alarm.setAddress(address);
        alarm.setRawData(rawData);
        alarm.setStatus(AlarmData.AlarmStatus.PENDING);
        alarm.setNotified(false);
        
        return save(alarm);
    }
}
