package com.gt06.service.repository;

import com.gt06.service.entity.Device;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 设备数据访问接口
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Repository
public interface DeviceRepository extends JpaRepository<Device, Long> {
    
    /**
     * 根据IMEI查找设备
     * 
     * @param imei 设备IMEI
     * @return 设备信息
     */
    Optional<Device> findByImei(String imei);
    
    /**
     * 根据IMEI和启用状态查找设备
     * 
     * @param imei 设备IMEI
     * @param enabled 是否启用
     * @return 设备信息
     */
    Optional<Device> findByImeiAndEnabled(String imei, Boolean enabled);
    
    /**
     * 根据设备状态查找设备列表
     * 
     * @param status 设备状态
     * @return 设备列表
     */
    List<Device> findByStatus(Device.DeviceStatus status);
    
    /**
     * 根据在线状态查找设备列表
     * 
     * @param online 是否在线
     * @return 设备列表
     */
    List<Device> findByOnline(Boolean online);
    
    /**
     * 根据启用状态查找设备列表
     * 
     * @param enabled 是否启用
     * @return 设备列表
     */
    List<Device> findByEnabled(Boolean enabled);
    
    /**
     * 根据车牌号查找设备
     * 
     * @param plateNumber 车牌号
     * @return 设备信息
     */
    Optional<Device> findByPlateNumber(String plateNumber);
    
    /**
     * 根据所有者查找设备列表
     * 
     * @param owner 所有者
     * @return 设备列表
     */
    List<Device> findByOwner(String owner);
    
    /**
     * 根据设备类型查找设备列表
     * 
     * @param deviceType 设备类型
     * @return 设备列表
     */
    List<Device> findByDeviceType(String deviceType);
    
    /**
     * 查找在线设备数量
     * 
     * @return 在线设备数量
     */
    @Query("SELECT COUNT(d) FROM Device d WHERE d.online = true AND d.enabled = true")
    long countOnlineDevices();
    
    /**
     * 查找离线设备数量
     * 
     * @return 离线设备数量
     */
    @Query("SELECT COUNT(d) FROM Device d WHERE d.online = false AND d.enabled = true")
    long countOfflineDevices();
    
    /**
     * 根据状态统计设备数量
     * 
     * @param status 设备状态
     * @return 设备数量
     */
    long countByStatus(Device.DeviceStatus status);
    
    /**
     * 查找服务即将到期的设备
     * 
     * @param expireTime 到期时间
     * @return 设备列表
     */
    @Query("SELECT d FROM Device d WHERE d.serviceExpireTime <= :expireTime AND d.enabled = true")
    List<Device> findDevicesWithExpiringSoon(@Param("expireTime") LocalDateTime expireTime);
    
    /**
     * 查找长时间离线的设备
     * 
     * @param offlineTime 离线时间阈值
     * @return 设备列表
     */
    @Query("SELECT d FROM Device d WHERE d.online = false AND d.lastOfflineTime <= :offlineTime AND d.enabled = true")
    List<Device> findLongTimeOfflineDevices(@Param("offlineTime") LocalDateTime offlineTime);
    
    /**
     * 根据关键字搜索设备
     * 
     * @param keyword 关键字
     * @param pageable 分页参数
     * @return 设备分页列表
     */
    @Query("SELECT d FROM Device d WHERE " +
           "d.imei LIKE %:keyword% OR " +
           "d.deviceName LIKE %:keyword% OR " +
           "d.plateNumber LIKE %:keyword% OR " +
           "d.owner LIKE %:keyword%")
    Page<Device> searchDevices(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 批量更新设备在线状态
     * 
     * @param imeis IMEI列表
     * @param online 在线状态
     * @param updateTime 更新时间
     * @return 更新数量
     */
    @Modifying
    @Query("UPDATE Device d SET d.online = :online, " +
           "d.lastOnlineTime = CASE WHEN :online = true THEN :updateTime ELSE d.lastOnlineTime END, " +
           "d.lastOfflineTime = CASE WHEN :online = false THEN :updateTime ELSE d.lastOfflineTime END, " +
           "d.status = CASE WHEN :online = true THEN 'NORMAL' ELSE 'OFFLINE' END " +
           "WHERE d.imei IN :imeis")
    int batchUpdateOnlineStatus(@Param("imeis") List<String> imeis, 
                               @Param("online") Boolean online, 
                               @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 批量更新设备状态
     * 
     * @param imeis IMEI列表
     * @param status 设备状态
     * @return 更新数量
     */
    @Modifying
    @Query("UPDATE Device d SET d.status = :status WHERE d.imei IN :imeis")
    int batchUpdateStatus(@Param("imeis") List<String> imeis, 
                         @Param("status") Device.DeviceStatus status);
    
    /**
     * 根据创建时间范围查找设备
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 设备列表
     */
    List<Device> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找最近注册的设备
     * 
     * @param limit 限制数量
     * @return 设备列表
     */
    @Query("SELECT d FROM Device d ORDER BY d.createTime DESC")
    List<Device> findRecentRegisteredDevices(Pageable pageable);
    
    /**
     * 统计设备注册趋势
     * 
     * @param startTime 开始时间
     * @return 统计结果
     */
    @Query("SELECT DATE(d.createTime) as date, COUNT(d) as count " +
           "FROM Device d WHERE d.createTime >= :startTime " +
           "GROUP BY DATE(d.createTime) ORDER BY DATE(d.createTime)")
    List<Object[]> getRegistrationTrend(@Param("startTime") LocalDateTime startTime);
    
    /**
     * 统计设备状态分布
     * 
     * @return 统计结果
     */
    @Query("SELECT d.status, COUNT(d) FROM Device d WHERE d.enabled = true GROUP BY d.status")
    List<Object[]> getStatusDistribution();
    
    /**
     * 统计设备类型分布
     * 
     * @return 统计结果
     */
    @Query("SELECT d.deviceType, COUNT(d) FROM Device d WHERE d.enabled = true GROUP BY d.deviceType")
    List<Object[]> getDeviceTypeDistribution();
    
    /**
     * 检查IMEI是否存在
     * 
     * @param imei 设备IMEI
     * @return 是否存在
     */
    boolean existsByImei(String imei);
    
    /**
     * 检查车牌号是否存在
     * 
     * @param plateNumber 车牌号
     * @return 是否存在
     */
    boolean existsByPlateNumber(String plateNumber);
    
    /**
     * 根据多个条件查询设备
     * 
     * @param status 设备状态
     * @param online 在线状态
     * @param deviceType 设备类型
     * @param owner 所有者
     * @param pageable 分页参数
     * @return 设备分页列表
     */
    @Query("SELECT d FROM Device d WHERE " +
           "(:status IS NULL OR d.status = :status) AND " +
           "(:online IS NULL OR d.online = :online) AND " +
           "(:deviceType IS NULL OR d.deviceType = :deviceType) AND " +
           "(:owner IS NULL OR d.owner LIKE %:owner%) AND " +
           "d.enabled = true")
    Page<Device> findDevicesByConditions(@Param("status") Device.DeviceStatus status,
                                        @Param("online") Boolean online,
                                        @Param("deviceType") String deviceType,
                                        @Param("owner") String owner,
                                        Pageable pageable);
}
