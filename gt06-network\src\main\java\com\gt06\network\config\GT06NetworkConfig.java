package com.gt06.network.config;

import com.gt06.network.codec.GT06MessageDecoder;
import com.gt06.network.codec.GT06MessageEncoder;
import com.gt06.network.handler.ConnectionHandler;
import com.gt06.network.handler.GT06ChannelInitializer;
import com.gt06.network.handler.GT06MessageHandler;
import com.gt06.network.server.GT06Server;
import com.gt06.network.session.SessionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

/**
 * GT06网络层配置类
 * 显式配置所有网络组件的依赖关系，确保正确的初始化顺序
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Configuration
public class GT06NetworkConfig {

    /**
     * 会话管理器Bean
     * 最基础的组件，其他组件都依赖它
     */
    @Bean
    public SessionManager sessionManager() {
        log.info("🔧 Creating SessionManager bean...");
        SessionManager sessionManager = new SessionManager();
        log.info("✅ SessionManager bean created successfully");
        return sessionManager;
    }

    /**
     * GT06消息解码器Bean
     */
    @Bean
    @DependsOn("sessionManager")
    public GT06MessageDecoder gt06MessageDecoder() {
        log.info("🔧 Creating GT06MessageDecoder bean...");
        GT06MessageDecoder decoder = new GT06MessageDecoder();
        log.info("✅ GT06MessageDecoder bean created successfully");
        return decoder;
    }

    /**
     * GT06消息编码器Bean
     */
    @Bean
    @DependsOn("sessionManager")
    public GT06MessageEncoder gt06MessageEncoder() {
        log.info("🔧 Creating GT06MessageEncoder bean...");
        GT06MessageEncoder encoder = new GT06MessageEncoder();
        log.info("✅ GT06MessageEncoder bean created successfully");
        return encoder;
    }

    /**
     * 连接处理器Bean
     */
    @Bean
    @DependsOn("sessionManager")
    public ConnectionHandler connectionHandler(SessionManager sessionManager) {
        log.info("🔧 Creating ConnectionHandler bean...");
        ConnectionHandler handler = new ConnectionHandler(sessionManager);
        log.info("✅ ConnectionHandler bean created successfully");
        return handler;
    }

    /**
     * GT06消息处理器Bean
     */
    @Bean
    @DependsOn("sessionManager")
    public GT06MessageHandler gt06MessageHandler(SessionManager sessionManager) {
        log.info("🔧 Creating GT06MessageHandler bean...");
        GT06MessageHandler handler = new GT06MessageHandler(sessionManager);
        log.info("✅ GT06MessageHandler bean created successfully");
        return handler;
    }

    /**
     * GT06通道初始化器Bean
     */
    @Bean
    @DependsOn({"gt06MessageDecoder", "gt06MessageEncoder", "gt06MessageHandler", "connectionHandler"})
    public GT06ChannelInitializer gt06ChannelInitializer(
            GT06MessageDecoder messageDecoder,
            GT06MessageEncoder messageEncoder,
            GT06MessageHandler messageHandler,
            ConnectionHandler connectionHandler,
            @Value("${gt06.network.server.read-timeout:300}") int readTimeoutSeconds,
            @Value("${gt06.network.server.write-timeout:60}") int writeTimeoutSeconds,
            @Value("${gt06.network.server.all-timeout:360}") int allTimeoutSeconds,
            @Value("${gt06.network.server.enable-logging:true}") boolean enableLogging,
            @Value("${gt06.network.server.log-level:DEBUG}") String logLevel) {

        log.info("🔧 Creating GT06ChannelInitializer bean...");
        GT06ChannelInitializer initializer = new GT06ChannelInitializer(
            messageDecoder, messageEncoder, messageHandler, connectionHandler,
            readTimeoutSeconds, writeTimeoutSeconds, allTimeoutSeconds,
            enableLogging, logLevel);
        log.info("✅ GT06ChannelInitializer bean created successfully");
        return initializer;
    }

    /**
     * GT06服务器Bean
     */
    @Bean
    @DependsOn("gt06ChannelInitializer")
    public GT06Server gt06Server(GT06ChannelInitializer channelInitializer,
                                  @Value("${gt06.network.server.port:8888}") int serverPort,
                                  @Value("${gt06.network.server.boss-threads:1}") int bossThreads,
                                  @Value("${gt06.network.server.worker-threads:0}") int workerThreads,
                                  @Value("${gt06.network.server.so-backlog:1024}") int soBacklog,
                                  @Value("${gt06.network.server.so-keepalive:true}") boolean soKeepAlive,
                                  @Value("${gt06.network.server.tcp-nodelay:true}") boolean tcpNoDelay,
                                  @Value("${gt06.network.server.auto-start:true}") boolean autoStart) {
        log.info("🔧 Creating GT06Server bean...");
        GT06Server server = new GT06Server(channelInitializer, serverPort, bossThreads,
                                          workerThreads, soBacklog, soKeepAlive, tcpNoDelay, autoStart);
        log.info("✅ GT06Server bean created successfully");
        return server;
    }
}
