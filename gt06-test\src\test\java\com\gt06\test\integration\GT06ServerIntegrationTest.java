package com.gt06.test.integration;

import com.gt06.test.client.GT06TestClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GT06服务器集成测试
 * 测试TCP服务器与客户端的完整通信流程
 * 
 * <AUTHOR> Test
 * @version 1.0.0
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class GT06ServerIntegrationTest {
    
    private static final String TEST_DEVICE_ID = "TEST_DEVICE_001";
    private static final String TEST_HOST = "localhost";
    private static final int TEST_PORT = 8888;
    
    private GT06TestClient testClient;
    
    @BeforeEach
    void setUp() {
        testClient = new GT06TestClient(TEST_HOST, TEST_PORT, TEST_DEVICE_ID);
    }
    
    @AfterEach
    void tearDown() {
        if (testClient != null) {
            testClient.disconnect();
        }
    }
    
    /**
     * 测试基本连接功能
     */
    @Test
    @Order(1)
    @DisplayName("测试TCP连接建立")
    void testConnection() {
        log.info("🧪 Testing TCP connection establishment...");
        
        // 测试连接
        boolean connected = testClient.connect();
        assertTrue(connected, "Should be able to connect to GT06 server");
        assertTrue(testClient.isConnected(), "Client should be in connected state");
        
        // 等待一段时间确保连接稳定
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        assertTrue(testClient.isConnected(), "Connection should remain stable");
        
        log.info("✅ TCP connection test passed");
    }
    
    /**
     * 测试登录消息
     */
    @Test
    @Order(2)
    @DisplayName("测试设备登录")
    void testLogin() {
        log.info("🧪 Testing device login...");
        
        // 建立连接
        assertTrue(testClient.connect(), "Should connect successfully");
        
        // 发送登录消息
        boolean loginSent = testClient.sendLogin();
        assertTrue(loginSent, "Login message should be sent successfully");
        
        // 等待服务器响应
        try {
            Thread.sleep(2000);
            byte[] response = testClient.readResponse();
            // 注意：这里可能需要根据实际服务器响应格式调整
            // assertNotNull(response, "Should receive login response from server");
        } catch (Exception e) {
            log.warn("No response received (this might be expected): {}", e.getMessage());
        }
        
        log.info("✅ Device login test passed");
    }
    
    /**
     * 测试位置上报
     */
    @Test
    @Order(3)
    @DisplayName("测试位置数据上报")
    void testLocationReporting() {
        log.info("🧪 Testing location reporting...");
        
        // 建立连接并登录
        assertTrue(testClient.connect(), "Should connect successfully");
        assertTrue(testClient.sendLogin(), "Should login successfully");
        
        // 等待登录完成
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 发送位置数据
        double latitude = 39.9042; // 北京天安门
        double longitude = 116.4074;
        int speed = 60; // 60 km/h
        
        boolean locationSent = testClient.sendLocation(latitude, longitude, speed);
        assertTrue(locationSent, "Location message should be sent successfully");
        
        // 发送多个位置点模拟移动
        for (int i = 1; i <= 3; i++) {
            try {
                Thread.sleep(1000);
                boolean sent = testClient.sendLocation(
                    latitude + i * 0.001, 
                    longitude + i * 0.001, 
                    speed + i * 5
                );
                assertTrue(sent, "Location message " + i + " should be sent successfully");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        log.info("✅ Location reporting test passed");
    }
    
    /**
     * 测试心跳机制
     */
    @Test
    @Order(4)
    @DisplayName("测试心跳机制")
    void testHeartbeat() {
        log.info("🧪 Testing heartbeat mechanism...");
        
        // 建立连接并登录
        assertTrue(testClient.connect(), "Should connect successfully");
        assertTrue(testClient.sendLogin(), "Should login successfully");
        
        // 手动发送心跳
        boolean heartbeatSent = testClient.sendHeartbeat();
        assertTrue(heartbeatSent, "Heartbeat message should be sent successfully");
        
        // 启动自动心跳
        testClient.startHeartbeat(5); // 每5秒一次心跳
        
        // 等待几个心跳周期
        try {
            Thread.sleep(12000); // 等待12秒，应该有2-3次心跳
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        assertTrue(testClient.isConnected(), "Connection should remain active during heartbeat");
        
        log.info("✅ Heartbeat mechanism test passed");
    }
    
    /**
     * 测试多客户端并发连接
     */
    @Test
    @Order(5)
    @DisplayName("测试多客户端并发连接")
    void testMultipleClients() {
        log.info("🧪 Testing multiple concurrent clients...");
        
        int clientCount = 5;
        List<GT06TestClient> clients = new ArrayList<>();
        List<CompletableFuture<Boolean>> futures = new ArrayList<>();
        
        try {
            // 创建多个客户端
            for (int i = 0; i < clientCount; i++) {
                String deviceId = "TEST_DEVICE_" + String.format("%03d", i + 1);
                GT06TestClient client = new GT06TestClient(TEST_HOST, TEST_PORT, deviceId);
                clients.add(client);
                
                // 异步连接和测试
                CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        // 连接
                        if (!client.connect()) {
                            return false;
                        }
                        
                        // 登录
                        if (!client.sendLogin()) {
                            return false;
                        }
                        
                        Thread.sleep(500);
                        
                        // 发送位置
                        if (!client.sendLocation(39.9042 + Math.random() * 0.01, 
                                                116.4074 + Math.random() * 0.01, 
                                                (int)(Math.random() * 100))) {
                            return false;
                        }
                        
                        Thread.sleep(500);
                        
                        // 发送心跳
                        return client.sendHeartbeat();
                        
                    } catch (Exception e) {
                        log.error("Client {} error: {}", client.getDeviceId(), e.getMessage());
                        return false;
                    }
                });
                
                futures.add(future);
            }
            
            // 等待所有客户端完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
            );
            
            allFutures.get(30, TimeUnit.SECONDS);
            
            // 检查结果
            int successCount = 0;
            for (CompletableFuture<Boolean> future : futures) {
                if (future.get()) {
                    successCount++;
                }
            }
            
            log.info("Successfully processed {}/{} clients", successCount, clientCount);
            assertTrue(successCount >= clientCount * 0.8, 
                "At least 80% of clients should succeed");
            
        } catch (Exception e) {
            log.error("Multiple clients test failed: {}", e.getMessage());
            fail("Multiple clients test should not throw exception");
        } finally {
            // 清理所有客户端
            for (GT06TestClient client : clients) {
                try {
                    client.disconnect();
                } catch (Exception e) {
                    log.warn("Error disconnecting client {}: {}", client.getDeviceId(), e.getMessage());
                }
            }
        }
        
        log.info("✅ Multiple clients test passed");
    }
    
    /**
     * 测试连接断开和重连
     */
    @Test
    @Order(6)
    @DisplayName("测试连接断开和重连")
    void testReconnection() {
        log.info("🧪 Testing connection and reconnection...");
        
        // 第一次连接
        assertTrue(testClient.connect(), "First connection should succeed");
        assertTrue(testClient.sendLogin(), "First login should succeed");
        
        // 主动断开
        testClient.disconnect();
        assertFalse(testClient.isConnected(), "Should be disconnected");
        
        // 等待一段时间
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 重新连接
        assertTrue(testClient.connect(), "Reconnection should succeed");
        assertTrue(testClient.sendLogin(), "Login after reconnection should succeed");
        assertTrue(testClient.sendLocation(39.9042, 116.4074, 50), 
            "Location reporting after reconnection should succeed");
        
        log.info("✅ Reconnection test passed");
    }
}
