package com.gt06.common.constants;

/**
 * GT06协议常量定义
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
public final class GT06Constants {
    
    private GT06Constants() {
        // 工具类，禁止实例化
    }
    
    // ==================== 协议基础常量 ====================
    
    /** 协议起始位 */
    public static final byte[] START_BITS = {0x78, 0x78};
    
    /** 协议停止位 */
    public static final byte[] STOP_BITS = {0x0D, 0x0A};
    
    /** 最小数据包长度 */
    public static final int MIN_PACKET_LENGTH = 10;
    
    /** 最大数据包长度 */
    public static final int MAX_PACKET_LENGTH = 1024;
    
    // ==================== 协议号定义 ====================
    
    /** 登录包 */
    public static final byte PROTOCOL_LOGIN = 0x01;
    
    /** 定位数据包 */
    public static final byte PROTOCOL_LOCATION = 0x12;
    
    /** 心跳包 */
    public static final byte PROTOCOL_HEARTBEAT = 0x13;
    
    /** 字符串信息 */
    public static final byte PROTOCOL_STRING_INFO = 0x15;
    
    /** 报警信息 */
    public static final byte PROTOCOL_ALARM = 0x16;
    
    /** LBS多基站 */
    public static final byte PROTOCOL_LBS_MULTI = 0x18;
    
    /** 查询地址 */
    public static final byte PROTOCOL_ADDRESS_QUERY = 0x1A;
    
    /** LBS+WIFI */
    public static final byte PROTOCOL_LBS_WIFI = 0x2C;
    
    /** 下发指令 */
    public static final byte PROTOCOL_COMMAND = (byte) 0x80;
    
    /** 录音协议 */
    public static final byte PROTOCOL_AUDIO = (byte) 0x8D;
    
    /** IMSI上报 */
    public static final byte PROTOCOL_IMSI = (byte) 0x90;
    
    /** ICCID上报 */
    public static final byte PROTOCOL_ICCID = (byte) 0x94;
    
    // ==================== 报警类型定义 ====================
    
    /** 正常 */
    public static final byte ALARM_NORMAL = 0x00;
    
    /** SOS报警 */
    public static final byte ALARM_SOS = 0x01;
    
    /** 断电报警 */
    public static final byte ALARM_POWER_OFF = 0x02;
    
    /** 震动报警 */
    public static final byte ALARM_VIBRATION = 0x03;
    
    /** 进围栏报警 */
    public static final byte ALARM_FENCE_IN = 0x04;
    
    /** 出围栏报警 */
    public static final byte ALARM_FENCE_OUT = 0x05;
    
    /** 超速报警 */
    public static final byte ALARM_OVERSPEED = 0x06;
    
    /** 防拆报警 */
    public static final byte ALARM_TAMPER = 0x13;
    
    /** 急加速报警 */
    public static final byte ALARM_RAPID_ACCELERATION = 0x26;
    
    /** 急减速报警 */
    public static final byte ALARM_RAPID_DECELERATION = 0x27;
    
    /** 急转弯报警 */
    public static final byte ALARM_SHARP_TURN = 0x28;
    
    /** 碰撞报警 */
    public static final byte ALARM_COLLISION = 0x29;
    
    // ==================== 设备状态定义 ====================
    
    /** 设备在线 */
    public static final int DEVICE_STATUS_ONLINE = 1;
    
    /** 设备离线 */
    public static final int DEVICE_STATUS_OFFLINE = 0;
    
    /** 设备未知状态 */
    public static final int DEVICE_STATUS_UNKNOWN = -1;
    
    // ==================== 网络配置常量 ====================
    
    /** 默认服务器端口 */
    public static final int DEFAULT_SERVER_PORT = 8888;
    
    /** 默认心跳超时时间（秒） */
    public static final int DEFAULT_HEARTBEAT_TIMEOUT = 300;
    
    /** 默认登录超时时间（秒） */
    public static final int DEFAULT_LOGIN_TIMEOUT = 30;
    
    /** 默认连接超时时间（秒） */
    public static final int DEFAULT_CONNECTION_TIMEOUT = 60;
    
    // ==================== 缓存键前缀 ====================
    
    /** 设备信息缓存前缀 */
    public static final String CACHE_DEVICE_PREFIX = "gt06:device:";
    
    /** 设备会话缓存前缀 */
    public static final String CACHE_SESSION_PREFIX = "gt06:session:";
    
    /** 设备状态缓存前缀 */
    public static final String CACHE_STATUS_PREFIX = "gt06:status:";
    
    /** 位置信息缓存前缀 */
    public static final String CACHE_LOCATION_PREFIX = "gt06:location:";
    
    // ==================== 消息队列主题 ====================
    
    /** 位置数据主题 */
    public static final String TOPIC_LOCATION = "gt06.location";
    
    /** 报警数据主题 */
    public static final String TOPIC_ALARM = "gt06.alarm";
    
    /** 设备状态主题 */
    public static final String TOPIC_DEVICE_STATUS = "gt06.device.status";
    
    /** 指令下发主题 */
    public static final String TOPIC_COMMAND = "gt06.command";
    
    // ==================== 系统配置常量 ====================
    
    /** 系统名称 */
    public static final String SYSTEM_NAME = "GT06 Protocol System";
    
    /** 系统版本 */
    public static final String SYSTEM_VERSION = "1.0.0";
    
    /** 协议版本 */
    public static final String PROTOCOL_VERSION = "1.02";
    
    /** 默认字符编码 */
    public static final String DEFAULT_CHARSET = "UTF-8";
    
    // ==================== 性能配置常量 ====================
    
    /** 默认工作线程数 */
    public static final int DEFAULT_WORKER_THREADS = Runtime.getRuntime().availableProcessors() * 2;
    
    /** 默认Boss线程数 */
    public static final int DEFAULT_BOSS_THREADS = 1;
    
    /** 默认连接队列大小 */
    public static final int DEFAULT_SO_BACKLOG = 1024;
    
    /** 默认批处理大小 */
    public static final int DEFAULT_BATCH_SIZE = 100;
    
    // ==================== 监控指标名称 ====================
    
    /** 连接数指标 */
    public static final String METRIC_CONNECTIONS = "gt06.connections";
    
    /** 消息处理速率指标 */
    public static final String METRIC_MESSAGE_RATE = "gt06.message.rate";
    
    /** 错误率指标 */
    public static final String METRIC_ERROR_RATE = "gt06.error.rate";
    
    /** 响应时间指标 */
    public static final String METRIC_RESPONSE_TIME = "gt06.response.time";
}
