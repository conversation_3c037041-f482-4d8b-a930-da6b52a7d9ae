<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="gt06-network" />
        <module name="gt06-admin" />
        <module name="gt06-common" />
        <module name="gt06-protocol" />
        <module name="gt06-web" />
        <module name="gt06-service" />
        <module name="gt06-test" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel target="8" />
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="gt06-admin" options="" />
      <module name="gt06-common" options="" />
      <module name="gt06-network" options="" />
      <module name="gt06-protocol" options="" />
      <module name="gt06-protocol-server" options="" />
      <module name="gt06-service" options="" />
      <module name="gt06-test" options="" />
      <module name="gt06-web" options="" />
    </option>
  </component>
</project>