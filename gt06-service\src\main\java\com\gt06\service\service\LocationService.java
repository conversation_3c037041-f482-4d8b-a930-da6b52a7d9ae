package com.gt06.service.service;

import com.gt06.service.entity.LocationData;
import com.gt06.service.repository.LocationDataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 位置数据服务
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LocationService {
    
    private final LocationDataRepository locationDataRepository;
    
    /**
     * 保存位置数据
     * 
     * @param locationData 位置数据
     * @return 保存后的位置数据
     */
    @Transactional
    public LocationData save(LocationData locationData) {
        // 验证位置数据有效性
        if (!isValidLocationData(locationData)) {
            log.warn("Invalid location data for IMEI: {}", locationData.getImei());
            locationData.setValidLocation(false);
        }
        
        // 设置地址信息（如果需要）
        if (locationData.getAddress() == null && locationData.isValidGpsCoordinate()) {
            // TODO: 调用地理编码服务获取地址
            // locationData.setAddress(geocodingService.getAddress(locationData.getLatitude(), locationData.getLongitude()));
        }
        
        LocationData saved = locationDataRepository.save(locationData);
        log.debug("Location data saved for IMEI: {} at {}", 
            locationData.getImei(), locationData.getLocationTime());
        
        return saved;
    }
    
    /**
     * 批量保存位置数据
     * 
     * @param locationDataList 位置数据列表
     * @return 保存后的位置数据列表
     */
    @Transactional
    public List<LocationData> saveAll(List<LocationData> locationDataList) {
        // 验证并处理位置数据
        locationDataList.forEach(locationData -> {
            if (!isValidLocationData(locationData)) {
                locationData.setValidLocation(false);
            }
        });
        
        List<LocationData> saved = locationDataRepository.saveAll(locationDataList);
        log.debug("Batch saved {} location records", saved.size());
        
        return saved;
    }
    
    /**
     * 根据IMEI获取最新位置
     * 
     * @param imei 设备IMEI
     * @return 最新位置数据
     */
    @Cacheable(value = "location:latest", key = "#imei")
    public Optional<LocationData> getLatestLocation(String imei) {
        return locationDataRepository.findLatestByImei(imei);
    }
    
    /**
     * 根据IMEI和时间范围查询位置数据
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 位置数据列表
     */
    public List<LocationData> findLocationsByTimeRange(String imei, LocalDateTime startTime, LocalDateTime endTime) {
        return locationDataRepository.findByImeiAndLocationTimeBetweenOrderByLocationTime(imei, startTime, endTime);
    }
    
    /**
     * 分页查询位置数据
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 位置数据分页列表
     */
    public Page<LocationData> findLocationsByTimeRange(String imei, LocalDateTime startTime, 
                                                      LocalDateTime endTime, Pageable pageable) {
        return locationDataRepository.findByImeiAndLocationTimeBetween(imei, startTime, endTime, pageable);
    }
    
    /**
     * 获取设备最近的位置记录
     * 
     * @param imei 设备IMEI
     * @param limit 限制数量
     * @return 最近位置记录列表
     */
    public List<LocationData> getRecentLocations(String imei, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return locationDataRepository.findRecentLocationsByImei(imei, pageable);
    }
    
    /**
     * 获取轨迹数据（用于轨迹回放）
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 轨迹数据列表
     */
    public List<LocationData> getTrackData(String imei, LocalDateTime startTime, LocalDateTime endTime) {
        return locationDataRepository.findTrackData(imei, startTime, endTime);
    }
    
    /**
     * 根据地理范围查询位置数据
     * 
     * @param minLat 最小纬度
     * @param maxLat 最大纬度
     * @param minLng 最小经度
     * @param maxLng 最大经度
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 位置数据列表
     */
    public List<LocationData> findLocationsByGeoBounds(BigDecimal minLat, BigDecimal maxLat,
                                                      BigDecimal minLng, BigDecimal maxLng,
                                                      LocalDateTime startTime, LocalDateTime endTime) {
        return locationDataRepository.findByGeoBounds(minLat, maxLat, minLng, maxLng, startTime, endTime);
    }
    
    /**
     * 查找超速记录
     * 
     * @param imei 设备IMEI
     * @param speedLimit 速度限制
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 超速记录列表
     */
    public List<LocationData> findOverspeedRecords(String imei, int speedLimit, 
                                                  LocalDateTime startTime, LocalDateTime endTime) {
        return locationDataRepository.findOverspeedRecords(imei, speedLimit, startTime, endTime);
    }
    
    /**
     * 查找停车记录
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 停车记录列表
     */
    public List<LocationData> findParkingRecords(String imei, LocalDateTime startTime, LocalDateTime endTime) {
        return locationDataRepository.findParkingRecords(imei, startTime, endTime);
    }
    
    /**
     * 计算设备行驶里程
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 行驶里程（公里）
     */
    public BigDecimal calculateMileage(String imei, LocalDateTime startTime, LocalDateTime endTime) {
        BigDecimal mileage = locationDataRepository.calculateMileage(imei, startTime, endTime);
        return mileage != null ? mileage : BigDecimal.ZERO;
    }
    
    /**
     * 计算设备平均速度
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均速度（km/h）
     */
    public Double calculateAverageSpeed(String imei, LocalDateTime startTime, LocalDateTime endTime) {
        Double avgSpeed = locationDataRepository.calculateAverageSpeed(imei, startTime, endTime);
        return avgSpeed != null ? avgSpeed : 0.0;
    }
    
    /**
     * 查找最高速度
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 最高速度（km/h）
     */
    public Integer findMaxSpeed(String imei, LocalDateTime startTime, LocalDateTime endTime) {
        Integer maxSpeed = locationDataRepository.findMaxSpeed(imei, startTime, endTime);
        return maxSpeed != null ? maxSpeed : 0;
    }
    
    /**
     * 计算GPS定位率
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return GPS定位率（0-1）
     */
    public Double calculateGpsFixRate(String imei, LocalDateTime startTime, LocalDateTime endTime) {
        Double fixRate = locationDataRepository.calculateGpsFixRate(imei, startTime, endTime);
        return fixRate != null ? fixRate : 0.0;
    }
    
    /**
     * 统计位置数据数量
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数据数量
     */
    public long countLocationData(String imei, LocalDateTime startTime, LocalDateTime endTime) {
        return locationDataRepository.countByImeiAndLocationTimeBetween(imei, startTime, endTime);
    }
    
    /**
     * 获取所有设备的最新位置
     * 
     * @return 最新位置数据列表
     */
    @Cacheable(value = "location:all_latest", key = "'all'")
    public List<LocationData> getAllLatestLocations() {
        return locationDataRepository.findLatestLocationForAllDevices();
    }
    
    /**
     * 获取每日位置数据统计
     * 
     * @param days 天数
     * @return 每日统计数据
     */
    public List<Object[]> getDailyLocationDataStats(int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        return locationDataRepository.getDailyLocationDataCount(startTime);
    }
    
    /**
     * 获取设备活跃度统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 设备活跃度统计
     */
    public List<Object[]> getDeviceActivityStats(LocalDateTime startTime, LocalDateTime endTime) {
        return locationDataRepository.getDeviceActivityStats(startTime, endTime);
    }
    
    /**
     * 删除过期的位置数据
     * 
     * @param retentionDays 保留天数
     * @return 删除数量
     */
    @Transactional
    public int deleteExpiredLocationData(int retentionDays) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(retentionDays);
        int deleted = locationDataRepository.deleteExpiredData(expireTime);
        log.info("Deleted {} expired location records older than {} days", deleted, retentionDays);
        return deleted;
    }
    
    /**
     * 根据IMEI删除位置数据
     * 
     * @param imei 设备IMEI
     * @return 删除数量
     */
    @Transactional
    public int deleteLocationDataByImei(String imei) {
        int deleted = locationDataRepository.deleteByImei(imei);
        log.info("Deleted {} location records for IMEI: {}", deleted, imei);
        return deleted;
    }
    
    /**
     * 验证位置数据有效性
     * 
     * @param locationData 位置数据
     * @return 是否有效
     */
    private boolean isValidLocationData(LocationData locationData) {
        if (locationData == null) {
            return false;
        }
        
        // 检查必要字段
        if (locationData.getImei() == null || locationData.getLocationTime() == null) {
            return false;
        }
        
        // 检查GPS坐标有效性
        if (locationData.getGpsFixed() != null && locationData.getGpsFixed()) {
            return locationData.isValidGpsCoordinate();
        }
        
        // 对于LBS定位，检查基站信息
        if (locationData.getLocationType() == LocationData.LocationType.LBS) {
            return locationData.getMcc() != null && locationData.getMnc() != null && 
                   locationData.getLac() != null && locationData.getCellId() != null;
        }
        
        return true;
    }
    
    /**
     * 计算两个位置之间的距离
     * 
     * @param location1 位置1
     * @param location2 位置2
     * @return 距离（米）
     */
    public double calculateDistance(LocationData location1, LocationData location2) {
        if (location1 == null || location2 == null) {
            return 0;
        }
        return location1.calculateDistance(location2);
    }
    
    /**
     * 分析行驶轨迹
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 轨迹分析结果
     */
    public TrackAnalysisResult analyzeTrack(String imei, LocalDateTime startTime, LocalDateTime endTime) {
        List<LocationData> trackData = getTrackData(imei, startTime, endTime);
        
        if (trackData.isEmpty()) {
            return new TrackAnalysisResult();
        }
        
        TrackAnalysisResult result = new TrackAnalysisResult();
        result.setTotalPoints(trackData.size());
        result.setStartTime(trackData.get(0).getLocationTime());
        result.setEndTime(trackData.get(trackData.size() - 1).getLocationTime());
        
        // 计算总距离
        double totalDistance = 0;
        for (int i = 1; i < trackData.size(); i++) {
            totalDistance += calculateDistance(trackData.get(i - 1), trackData.get(i));
        }
        result.setTotalDistance(totalDistance);
        
        // 计算平均速度
        Double avgSpeed = calculateAverageSpeed(imei, startTime, endTime);
        result.setAverageSpeed(avgSpeed);
        
        // 查找最高速度
        Integer maxSpeed = findMaxSpeed(imei, startTime, endTime);
        result.setMaxSpeed(maxSpeed);
        
        // 计算GPS定位率
        Double gpsFixRate = calculateGpsFixRate(imei, startTime, endTime);
        result.setGpsFixRate(gpsFixRate);
        
        return result;
    }
    
    /**
     * 轨迹分析结果
     */
    public static class TrackAnalysisResult {
        private int totalPoints;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private double totalDistance;
        private Double averageSpeed;
        private Integer maxSpeed;
        private Double gpsFixRate;
        
        // Getters and Setters
        public int getTotalPoints() { return totalPoints; }
        public void setTotalPoints(int totalPoints) { this.totalPoints = totalPoints; }
        
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        
        public double getTotalDistance() { return totalDistance; }
        public void setTotalDistance(double totalDistance) { this.totalDistance = totalDistance; }
        
        public Double getAverageSpeed() { return averageSpeed; }
        public void setAverageSpeed(Double averageSpeed) { this.averageSpeed = averageSpeed; }
        
        public Integer getMaxSpeed() { return maxSpeed; }
        public void setMaxSpeed(Integer maxSpeed) { this.maxSpeed = maxSpeed; }
        
        public Double getGpsFixRate() { return gpsFixRate; }
        public void setGpsFixRate(Double gpsFixRate) { this.gpsFixRate = gpsFixRate; }
    }
}
