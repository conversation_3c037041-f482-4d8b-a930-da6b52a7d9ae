package com.gt06.protocol.message;

import com.gt06.common.constants.GT06Constants;
import com.gt06.common.util.CRCUtil;
import io.netty.buffer.ByteBuf;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * GT06协议消息抽象基类
 * 定义所有GT06消息的通用结构和行为
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Data
public abstract class GT06Message {
    
    /** 起始位：固定为0x78 0x78 */
    protected byte[] startBits = GT06Constants.START_BITS;
    
    /** 包长度：协议号+信息内容+信息序列号+错误校验的长度 */
    protected int length;
    
    /** 协议号：标识消息类型 */
    protected byte protocolNumber;
    
    /** 信息内容：具体的数据内容，由子类实现 */
    protected byte[] content;
    
    /** 信息序列号：递增的序列号，用于消息确认 */
    protected int serialNumber;
    
    /** 错误校验：CRC-ITU校验码 */
    protected int crc;
    
    /** 停止位：固定为0x0D 0x0A */
    protected byte[] stopBits = GT06Constants.STOP_BITS;
    
    /** 消息接收时间 */
    protected LocalDateTime receivedTime;
    
    /** 设备IMEI号（从会话中获取） */
    protected String deviceImei;
    
    /**
     * 构造函数
     * 
     * @param protocolNumber 协议号
     */
    protected GT06Message(byte protocolNumber) {
        this.protocolNumber = protocolNumber;
        this.receivedTime = LocalDateTime.now();
    }
    
    /**
     * 抽象方法：编码消息为字节数组
     * 
     * @return 编码后的字节数组
     */
    public abstract byte[] encode();
    
    /**
     * 抽象方法：从ByteBuf解码消息
     * 
     * @param buffer 包含消息数据的ByteBuf
     */
    public abstract void decode(ByteBuf buffer);
    
    /**
     * 抽象方法：验证消息的有效性
     * 
     * @return 消息是否有效
     */
    public abstract boolean validate();
    
    /**
     * 抽象方法：获取消息的字符串表示（用于日志）
     * 
     * @return 消息描述
     */
    public abstract String getMessageDescription();
    
    /**
     * 计算并设置CRC校验码
     * 
     * @param data 待校验的数据（不包含CRC字段）
     */
    protected void calculateAndSetCRC(byte[] data) {
        if (data != null && data.length >= 3) {
            // CRC计算范围：从包长度字段开始到CRC字段之前
            this.crc = CRCUtil.calculateCRC(data, 2, data.length - 2);
        }
    }
    
    /**
     * 验证CRC校验码
     * 
     * @param packet 完整的数据包
     * @return 校验是否通过
     */
    protected boolean verifyCRC(byte[] packet) {
        return CRCUtil.verifyCRC(packet);
    }
    
    /**
     * 创建基础的数据包头部
     * 包含：起始位 + 包长度 + 协议号
     * 
     * @param contentLength 内容长度
     * @return 头部字节数组
     */
    protected byte[] createPacketHeader(int contentLength) {
        // 包长度 = 协议号(1) + 内容长度 + 序列号(2) + CRC(2)
        this.length = 1 + contentLength + 2 + 2;
        
        return new byte[] {
            startBits[0],           // 起始位1
            startBits[1],           // 起始位2
            (byte) length,          // 包长度
            protocolNumber          // 协议号
        };
    }
    
    /**
     * 创建数据包尾部
     * 包含：序列号 + CRC + 停止位
     * 
     * @param dataForCRC 用于计算CRC的数据
     * @return 尾部字节数组
     */
    protected byte[] createPacketTail(byte[] dataForCRC) {
        // 计算CRC
        calculateAndSetCRC(dataForCRC);
        
        return new byte[] {
            (byte) ((serialNumber >> 8) & 0xFF),    // 序列号高字节
            (byte) (serialNumber & 0xFF),           // 序列号低字节
            (byte) ((crc >> 8) & 0xFF),             // CRC高字节
            (byte) (crc & 0xFF),                    // CRC低字节
            stopBits[0],                            // 停止位1
            stopBits[1]                             // 停止位2
        };
    }
    
    /**
     * 从ByteBuf读取基础包头信息
     * 
     * @param buffer ByteBuf对象
     */
    protected void readPacketHeader(ByteBuf buffer) {
        // 跳过起始位（已经验证过）
        buffer.skipBytes(2);
        
        // 读取包长度
        this.length = buffer.readUnsignedByte();
        
        // 读取协议号（已知，但验证一致性）
        byte readProtocolNumber = buffer.readByte();
        if (readProtocolNumber != this.protocolNumber) {
            throw new IllegalArgumentException(
                String.format("Protocol number mismatch: expected %02X, got %02X", 
                    this.protocolNumber & 0xFF, readProtocolNumber & 0xFF));
        }
    }
    
    /**
     * 从ByteBuf读取包尾信息
     * 
     * @param buffer ByteBuf对象
     */
    protected void readPacketTail(ByteBuf buffer) {
        // 读取序列号
        this.serialNumber = buffer.readUnsignedShort();
        
        // 读取CRC
        this.crc = buffer.readUnsignedShort();
        
        // 跳过停止位（在解码器中已经验证）
        buffer.skipBytes(2);
    }
    
    /**
     * 获取协议号的十六进制字符串表示
     * 
     * @return 协议号十六进制字符串
     */
    public String getProtocolNumberHex() {
        return String.format("0x%02X", protocolNumber & 0xFF);
    }
    
    /**
     * 检查消息是否需要响应
     * 
     * @return 是否需要响应
     */
    public boolean needsResponse() {
        // 默认情况下，除了响应消息本身，其他消息都需要响应
        return protocolNumber != GT06Constants.PROTOCOL_STRING_INFO;
    }
    
    /**
     * 创建对应的响应消息
     * 
     * @return 响应消息，如果不需要响应则返回null
     */
    public GT06Message createResponse() {
        if (!needsResponse()) {
            return null;
        }
        
        // 默认创建简单的确认响应
        return new ResponseMessage(this.protocolNumber, this.serialNumber);
    }
    
    /**
     * 获取消息的唯一标识
     * 
     * @return 消息唯一标识
     */
    public String getMessageId() {
        return String.format("%s-%s-%d", 
            deviceImei != null ? deviceImei : "UNKNOWN",
            getProtocolNumberHex(),
            serialNumber);
    }
    
    @Override
    public String toString() {
        return String.format("GT06Message{protocol=%s, serial=%d, imei=%s, time=%s, desc=%s}",
            getProtocolNumberHex(),
            serialNumber,
            deviceImei,
            receivedTime,
            getMessageDescription());
    }
}
