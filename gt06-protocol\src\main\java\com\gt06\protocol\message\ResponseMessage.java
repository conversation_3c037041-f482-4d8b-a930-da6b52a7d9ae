package com.gt06.protocol.message;

import com.gt06.common.constants.GT06Constants;
import com.gt06.common.util.ByteUtil;
import io.netty.buffer.ByteBuf;
import lombok.Getter;
import lombok.Setter;

/**
 * GT06协议响应消息
 * 用于服务器向设备发送确认响应
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Getter
@Setter
public class ResponseMessage extends GT06Message {
    
    /** 原始协议号（被响应的消息协议号） */
    private byte originalProtocolNumber;
    
    /** 原始序列号（被响应的消息序列号） */
    private int originalSerialNumber;
    
    /**
     * 构造函数
     * 
     * @param originalProtocolNumber 原始消息的协议号
     * @param originalSerialNumber 原始消息的序列号
     */
    public ResponseMessage(byte originalProtocolNumber, int originalSerialNumber) {
        super(GT06Constants.PROTOCOL_STRING_INFO); // 响应消息使用0x15协议号
        this.originalProtocolNumber = originalProtocolNumber;
        this.originalSerialNumber = originalSerialNumber;
        this.serialNumber = originalSerialNumber; // 响应消息使用相同的序列号
    }
    
    /**
     * 默认构造函数（用于解码）
     */
    public ResponseMessage() {
        super(GT06Constants.PROTOCOL_STRING_INFO);
    }
    
    @Override
    public byte[] encode() {
        // 响应消息的内容通常为空或包含简单的确认信息
        byte[] content = new byte[0];
        
        // 创建包头
        byte[] header = createPacketHeader(content.length);
        
        // 组装完整数据包（不包含CRC和停止位）
        byte[] dataForCRC = ByteUtil.concat(header, content);
        
        // 创建包尾
        byte[] tail = createPacketTail(dataForCRC);
        
        // 返回完整数据包
        return ByteUtil.concat(dataForCRC, tail);
    }
    
    @Override
    public void decode(ByteBuf buffer) {
        // 读取包头
        readPacketHeader(buffer);
        
        // 响应消息通常没有内容，或内容很简单
        int contentLength = length - 1 - 2 - 2; // 总长度 - 协议号 - 序列号 - CRC
        if (contentLength > 0) {
            this.content = new byte[contentLength];
            buffer.readBytes(this.content);
        }
        
        // 读取包尾
        readPacketTail(buffer);
    }
    
    @Override
    public boolean validate() {
        // 验证基本字段
        if (protocolNumber != GT06Constants.PROTOCOL_STRING_INFO) {
            return false;
        }
        
        if (serialNumber < 0 || serialNumber > 0xFFFF) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public String getMessageDescription() {
        return String.format("Response to protocol 0x%02X, serial %d", 
            originalProtocolNumber & 0xFF, originalSerialNumber);
    }
    
    @Override
    public boolean needsResponse() {
        // 响应消息本身不需要再次响应
        return false;
    }
    
    @Override
    public GT06Message createResponse() {
        // 响应消息不需要创建响应
        return null;
    }
    
    /**
     * 创建登录响应消息
     * 
     * @param originalSerialNumber 原始登录消息的序列号
     * @return 登录响应消息
     */
    public static ResponseMessage createLoginResponse(int originalSerialNumber) {
        ResponseMessage response = new ResponseMessage(
            GT06Constants.PROTOCOL_LOGIN, 
            originalSerialNumber
        );
        return response;
    }
    
    /**
     * 创建心跳响应消息
     * 
     * @param originalSerialNumber 原始心跳消息的序列号
     * @return 心跳响应消息
     */
    public static ResponseMessage createHeartbeatResponse(int originalSerialNumber) {
        ResponseMessage response = new ResponseMessage(
            GT06Constants.PROTOCOL_HEARTBEAT, 
            originalSerialNumber
        );
        return response;
    }
    
    /**
     * 创建位置数据响应消息
     * 
     * @param originalSerialNumber 原始位置消息的序列号
     * @return 位置数据响应消息
     */
    public static ResponseMessage createLocationResponse(int originalSerialNumber) {
        ResponseMessage response = new ResponseMessage(
            GT06Constants.PROTOCOL_LOCATION, 
            originalSerialNumber
        );
        return response;
    }
    
    /**
     * 创建报警响应消息
     * 
     * @param originalSerialNumber 原始报警消息的序列号
     * @return 报警响应消息
     */
    public static ResponseMessage createAlarmResponse(int originalSerialNumber) {
        ResponseMessage response = new ResponseMessage(
            GT06Constants.PROTOCOL_ALARM, 
            originalSerialNumber
        );
        return response;
    }
    
    /**
     * 创建错误响应消息
     *
     * @param originalProtocolNumber 原始消息协议号
     * @param originalSerialNumber 原始消息序列号
     * @return 错误响应消息
     */
    public static ResponseMessage createErrorResponse(byte originalProtocolNumber, int originalSerialNumber) {
        return new ResponseMessage(originalProtocolNumber, originalSerialNumber);
    }

    /**
     * 创建通用响应消息
     *
     * @param originalProtocolNumber 原始消息协议号
     * @param originalSerialNumber 原始消息序列号
     * @return 通用响应消息
     */
    public static ResponseMessage createGenericResponse(byte originalProtocolNumber, int originalSerialNumber) {
        return new ResponseMessage(originalProtocolNumber, originalSerialNumber);
    }
}
