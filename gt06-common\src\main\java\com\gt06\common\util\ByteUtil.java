package com.gt06.common.util;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.time.LocalDateTime;

/**
 * 字节操作工具类
 * 提供GT06协议中常用的字节操作方法
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
public final class ByteUtil {
    
    private ByteUtil() {
        // 工具类，禁止实例化
    }
    
    /**
     * 将字节数组转换为十六进制字符串
     * 
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X", b & 0xFF));
        }
        
        return result.toString();
    }
    
    /**
     * 将字节数组转换为带空格的十六进制字符串
     * 
     * @param bytes 字节数组
     * @return 带空格的十六进制字符串
     */
    public static String bytesToHexWithSpace(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            if (i > 0) {
                result.append(" ");
            }
            result.append(String.format("%02X", bytes[i] & 0xFF));
        }
        
        return result.toString();
    }
    
    /**
     * 将十六进制字符串转换为字节数组
     * 
     * @param hex 十六进制字符串
     * @return 字节数组
     */
    public static byte[] hexToBytes(String hex) {
        if (hex == null || hex.isEmpty()) {
            return new byte[0];
        }
        
        // 移除空格和其他分隔符
        hex = hex.replaceAll("[\\s-:]", "");
        
        if (hex.length() % 2 != 0) {
            throw new IllegalArgumentException("Hex string length must be even");
        }
        
        byte[] result = new byte[hex.length() / 2];
        for (int i = 0; i < result.length; i++) {
            result[i] = (byte) Integer.parseInt(hex.substring(i * 2, i * 2 + 2), 16);
        }
        
        return result;
    }
    
    /**
     * 将int值转换为2字节数组（大端序）
     * 
     * @param value int值
     * @return 2字节数组
     */
    public static byte[] intToBytes2(int value) {
        return new byte[] {
            (byte) ((value >> 8) & 0xFF),
            (byte) (value & 0xFF)
        };
    }
    
    /**
     * 将int值转换为4字节数组（大端序）
     * 
     * @param value int值
     * @return 4字节数组
     */
    public static byte[] intToBytes4(int value) {
        return new byte[] {
            (byte) ((value >> 24) & 0xFF),
            (byte) ((value >> 16) & 0xFF),
            (byte) ((value >> 8) & 0xFF),
            (byte) (value & 0xFF)
        };
    }
    
    /**
     * 将2字节数组转换为int值（大端序）
     * 
     * @param bytes 2字节数组
     * @param offset 偏移量
     * @return int值
     */
    public static int bytes2ToInt(byte[] bytes, int offset) {
        if (bytes == null || offset < 0 || offset + 1 >= bytes.length) {
            throw new IllegalArgumentException("Invalid bytes array or offset");
        }
        
        return ((bytes[offset] & 0xFF) << 8) | (bytes[offset + 1] & 0xFF);
    }
    
    /**
     * 将4字节数组转换为int值（大端序）
     * 
     * @param bytes 4字节数组
     * @param offset 偏移量
     * @return int值
     */
    public static int bytes4ToInt(byte[] bytes, int offset) {
        if (bytes == null || offset < 0 || offset + 3 >= bytes.length) {
            throw new IllegalArgumentException("Invalid bytes array or offset");
        }
        
        return ((bytes[offset] & 0xFF) << 24) |
               ((bytes[offset + 1] & 0xFF) << 16) |
               ((bytes[offset + 2] & 0xFF) << 8) |
               (bytes[offset + 3] & 0xFF);
    }
    
    /**
     * 将3字节数组转换为int值（大端序）
     * 用于GT06协议中的基站ID等字段
     * 
     * @param bytes 字节数组
     * @param offset 偏移量
     * @return int值
     */
    public static int bytes3ToInt(byte[] bytes, int offset) {
        if (bytes == null || offset < 0 || offset + 2 >= bytes.length) {
            throw new IllegalArgumentException("Invalid bytes array or offset");
        }
        
        return ((bytes[offset] & 0xFF) << 16) |
               ((bytes[offset + 1] & 0xFF) << 8) |
               (bytes[offset + 2] & 0xFF);
    }
    
    /**
     * 将long值转换为8字节数组（大端序）
     * 
     * @param value long值
     * @return 8字节数组
     */
    public static byte[] longToBytes8(long value) {
        return ByteBuffer.allocate(8)
                .order(ByteOrder.BIG_ENDIAN)
                .putLong(value)
                .array();
    }
    
    /**
     * 将8字节数组转换为long值（大端序）
     * 
     * @param bytes 8字节数组
     * @param offset 偏移量
     * @return long值
     */
    public static long bytes8ToLong(byte[] bytes, int offset) {
        if (bytes == null || offset < 0 || offset + 7 >= bytes.length) {
            throw new IllegalArgumentException("Invalid bytes array or offset");
        }
        
        return ByteBuffer.wrap(bytes, offset, 8)
                .order(ByteOrder.BIG_ENDIAN)
                .getLong();
    }
    
    /**
     * 将LocalDateTime转换为GT06协议的6字节时间格式
     * 格式：年(1字节) 月(1字节) 日(1字节) 时(1字节) 分(1字节) 秒(1字节)
     * 
     * @param dateTime 时间对象
     * @return 6字节时间数组
     */
    public static byte[] dateTimeToBytes(LocalDateTime dateTime) {
        if (dateTime == null) {
            throw new IllegalArgumentException("DateTime cannot be null");
        }
        
        return new byte[] {
            (byte) (dateTime.getYear() - 2000), // 年份减去2000
            (byte) dateTime.getMonthValue(),
            (byte) dateTime.getDayOfMonth(),
            (byte) dateTime.getHour(),
            (byte) dateTime.getMinute(),
            (byte) dateTime.getSecond()
        };
    }
    
    /**
     * 将6字节时间数组转换为LocalDateTime
     * 
     * @param bytes 时间字节数组
     * @param offset 偏移量
     * @return LocalDateTime对象
     */
    public static LocalDateTime bytesToDateTime(byte[] bytes, int offset) {
        if (bytes == null || offset < 0 || offset + 5 >= bytes.length) {
            throw new IllegalArgumentException("Invalid bytes array or offset");
        }
        
        int year = 2000 + (bytes[offset] & 0xFF);
        int month = bytes[offset + 1] & 0xFF;
        int day = bytes[offset + 2] & 0xFF;
        int hour = bytes[offset + 3] & 0xFF;
        int minute = bytes[offset + 4] & 0xFF;
        int second = bytes[offset + 5] & 0xFF;
        
        return LocalDateTime.of(year, month, day, hour, minute, second);
    }
    
    /**
     * 检查字节数组是否以指定的字节序列开始
     * 
     * @param data 数据数组
     * @param prefix 前缀数组
     * @return 是否匹配
     */
    public static boolean startsWith(byte[] data, byte[] prefix) {
        if (data == null || prefix == null || data.length < prefix.length) {
            return false;
        }
        
        for (int i = 0; i < prefix.length; i++) {
            if (data[i] != prefix[i]) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查字节数组是否以指定的字节序列结束
     * 
     * @param data 数据数组
     * @param suffix 后缀数组
     * @return 是否匹配
     */
    public static boolean endsWith(byte[] data, byte[] suffix) {
        if (data == null || suffix == null || data.length < suffix.length) {
            return false;
        }
        
        int offset = data.length - suffix.length;
        for (int i = 0; i < suffix.length; i++) {
            if (data[offset + i] != suffix[i]) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 合并多个字节数组
     * 
     * @param arrays 字节数组列表
     * @return 合并后的字节数组
     */
    public static byte[] concat(byte[]... arrays) {
        if (arrays == null || arrays.length == 0) {
            return new byte[0];
        }
        
        int totalLength = 0;
        for (byte[] array : arrays) {
            if (array != null) {
                totalLength += array.length;
            }
        }
        
        byte[] result = new byte[totalLength];
        int offset = 0;
        
        for (byte[] array : arrays) {
            if (array != null) {
                System.arraycopy(array, 0, result, offset, array.length);
                offset += array.length;
            }
        }
        
        return result;
    }
}
