package com.gt06.web.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redisson配置类
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.redisson.config:}")
    private String redissonConfig;

    /**
     * Redisson客户端配置
     */
    @Bean(destroyMethod = "shutdown")
    public RedissonClient redissonClient() {
        Config config = new Config();

        // 单机模式配置
        config.useSingleServer()
                .setAddress("redis://************:6379")
                .setPassword("yunqu168")
                .setDatabase(2)
                .setConnectionPoolSize(10)
                .setConnectionMinimumIdleSize(5)
                .setIdleConnectionTimeout(10000)
                .setConnectTimeout(10000)
                .setTimeout(3000)
                .setRetryAttempts(3)
                .setRetryInterval(1500);

        return Redisson.create(config);
    }

}
