package com.gt06.common.util;

/**
 * BCD编码解码工具类
 * 用于GT06协议中IMEI等数据的BCD编码处理
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
public final class BCDUtil {
    
    private BCDUtil() {
        // 工具类，禁止实例化
    }
    
    /**
     * 将BCD编码的字节数组解码为字符串
     * 
     * @param bcdBytes BCD编码的字节数组
     * @return 解码后的字符串
     */
    public static String decode(byte[] bcdBytes) {
        if (bcdBytes == null || bcdBytes.length == 0) {
            return "";
        }
        
        StringBuilder result = new StringBuilder();
        
        for (byte bcdByte : bcdBytes) {
            // 提取高4位
            int high = (bcdByte >> 4) & 0x0F;
            // 提取低4位
            int low = bcdByte & 0x0F;
            
            // 转换为字符（忽略无效的BCD值）
            if (high <= 9) {
                result.append((char) ('0' + high));
            }
            if (low <= 9) {
                result.append((char) ('0' + low));
            }
        }
        
        return result.toString();
    }
    
    /**
     * 将字符串编码为BCD格式的字节数组
     * 
     * @param str 待编码的数字字符串
     * @return BCD编码的字节数组
     */
    public static byte[] encode(String str) {
        if (str == null || str.isEmpty()) {
            return new byte[0];
        }
        
        // 过滤非数字字符
        String digits = str.replaceAll("[^0-9]", "");
        
        // 如果长度为奇数，在前面补0
        if (digits.length() % 2 != 0) {
            digits = "0" + digits;
        }
        
        byte[] result = new byte[digits.length() / 2];
        
        for (int i = 0; i < result.length; i++) {
            int high = Character.getNumericValue(digits.charAt(i * 2));
            int low = Character.getNumericValue(digits.charAt(i * 2 + 1));
            
            result[i] = (byte) ((high << 4) | low);
        }
        
        return result;
    }
    
    /**
     * 将IMEI字符串编码为8字节BCD数组
     * GT06协议中IMEI使用8字节BCD编码
     * 
     * @param imei IMEI字符串（15位数字）
     * @return 8字节BCD编码数组
     */
    public static byte[] encodeIMEI(String imei) {
        if (imei == null || imei.length() != 15) {
            throw new IllegalArgumentException("IMEI must be 15 digits");
        }
        
        // 验证IMEI只包含数字
        if (!imei.matches("\\d{15}")) {
            throw new IllegalArgumentException("IMEI must contain only digits");
        }
        
        // IMEI编码为8字节：前7字节存储14位数字，最后1字节存储第15位数字和填充
        byte[] result = new byte[8];
        
        // 编码前14位数字
        for (int i = 0; i < 7; i++) {
            int high = Character.getNumericValue(imei.charAt(i * 2));
            int low = Character.getNumericValue(imei.charAt(i * 2 + 1));
            result[i] = (byte) ((high << 4) | low);
        }
        
        // 编码第15位数字，低4位填充0
        int lastDigit = Character.getNumericValue(imei.charAt(14));
        result[7] = (byte) (lastDigit << 4);
        
        return result;
    }
    
    /**
     * 将8字节BCD数组解码为IMEI字符串
     * 
     * @param bcdBytes 8字节BCD编码数组
     * @return IMEI字符串
     */
    public static String decodeIMEI(byte[] bcdBytes) {
        if (bcdBytes == null || bcdBytes.length != 8) {
            throw new IllegalArgumentException("IMEI BCD array must be 8 bytes");
        }
        
        StringBuilder result = new StringBuilder();
        
        // 解码前7字节（14位数字）
        for (int i = 0; i < 7; i++) {
            int high = (bcdBytes[i] >> 4) & 0x0F;
            int low = bcdBytes[i] & 0x0F;
            
            if (high > 9 || low > 9) {
                throw new IllegalArgumentException("Invalid BCD data in IMEI");
            }
            
            result.append(high).append(low);
        }
        
        // 解码第8字节的高4位（第15位数字）
        int lastDigit = (bcdBytes[7] >> 4) & 0x0F;
        if (lastDigit > 9) {
            throw new IllegalArgumentException("Invalid BCD data in IMEI last digit");
        }
        
        result.append(lastDigit);
        
        return result.toString();
    }
    
    /**
     * 验证BCD编码数据的有效性
     * 
     * @param bcdBytes BCD编码的字节数组
     * @return 是否为有效的BCD数据
     */
    public static boolean isValidBCD(byte[] bcdBytes) {
        if (bcdBytes == null) {
            return false;
        }
        
        for (byte bcdByte : bcdBytes) {
            int high = (bcdByte >> 4) & 0x0F;
            int low = bcdByte & 0x0F;
            
            // BCD中每个半字节的值应该在0-9之间
            if (high > 9 || low > 9) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 将BCD字节数组转换为十六进制字符串（用于调试）
     * 
     * @param bcdBytes BCD字节数组
     * @return 十六进制字符串
     */
    public static String toHexString(byte[] bcdBytes) {
        if (bcdBytes == null || bcdBytes.length == 0) {
            return "";
        }
        
        StringBuilder result = new StringBuilder();
        for (byte b : bcdBytes) {
            result.append(String.format("%02X ", b & 0xFF));
        }
        
        return result.toString().trim();
    }
    
    /**
     * 从十六进制字符串解析BCD字节数组（用于测试）
     * 
     * @param hexString 十六进制字符串（如"12 34 56"）
     * @return BCD字节数组
     */
    public static byte[] fromHexString(String hexString) {
        if (hexString == null || hexString.trim().isEmpty()) {
            return new byte[0];
        }
        
        String[] hexBytes = hexString.trim().split("\\s+");
        byte[] result = new byte[hexBytes.length];
        
        for (int i = 0; i < hexBytes.length; i++) {
            result[i] = (byte) Integer.parseInt(hexBytes[i], 16);
        }
        
        return result;
    }
}
