package com.gt06.protocol.message;

import com.gt06.common.constants.GT06Constants;
import com.gt06.common.util.ByteUtil;
import io.netty.buffer.ByteBuf;
import lombok.Getter;
import lombok.Setter;

import java.nio.charset.StandardCharsets;

/**
 * GT06协议指令消息 (0x80)
 * 服务器向设备发送的控制指令
 * 
 * 消息格式：
 * 起始位(2) + 包长度(1) + 协议号(1) + 指令长度(1) + 指令内容(N) + 
 * 序列号(2) + CRC(2) + 停止位(2)
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Getter
@Setter
public class CommandMessage extends GT06Message {
    
    /** 指令内容 */
    private String commandContent;
    
    /** 指令类型 */
    private CommandType commandType;
    
    /** 指令参数 */
    private String[] parameters;
    
    /** 是否需要确认响应 */
    private boolean needsConfirmation;
    
    /**
     * 指令类型枚举
     */
    public enum CommandType {
        // 基础控制指令
        RESET("RESET", "重启设备", true),
        FACTORY_RESET("FACTORY", "恢复出厂设置", true),
        
        // 定位相关指令
        LOCATION_REQUEST("WHERE", "请求位置信息", false),
        GPS_ON("GPSON", "开启GPS", true),
        GPS_OFF("GPSOFF", "关闭GPS", true),
        
        // 报警相关指令
        ALARM_ARM("ARM", "设防", true),
        ALARM_DISARM("DISARM", "撤防", true),
        SOS_NUMBER("SOS", "设置SOS号码", true),
        
        // 网络相关指令
        APN_SET("APN", "设置APN", true),
        SERVER_SET("SERVER", "设置服务器", true),
        
        // 参数配置指令
        UPLOAD_INTERVAL("UPLOAD", "设置上传间隔", true),
        HEARTBEAT_INTERVAL("HBT", "设置心跳间隔", true),
        TIMEZONE_SET("TIMEZONE", "设置时区", true),
        
        // 控制指令
        OIL_CUT("RELAY", "断油电", true),
        OIL_RESUME("RELAY", "恢复油电", true),
        MONITOR("MONITOR", "监听", false),
        
        // 查询指令
        STATUS_QUERY("STATUS", "查询状态", false),
        VERSION_QUERY("VERSION", "查询版本", false),
        IMEI_QUERY("IMEI", "查询IMEI", false),
        
        // 自定义指令
        CUSTOM("CUSTOM", "自定义指令", false);
        
        private final String command;
        private final String description;
        private final boolean needsConfirmation;
        
        CommandType(String command, String description, boolean needsConfirmation) {
            this.command = command;
            this.description = description;
            this.needsConfirmation = needsConfirmation;
        }
        
        public String getCommand() { return command; }
        public String getDescription() { return description; }
        public boolean needsConfirmation() { return needsConfirmation; }
        
        public static CommandType fromCommand(String command) {
            if (command == null) return CUSTOM;
            
            String upperCommand = command.toUpperCase();
            for (CommandType type : values()) {
                if (type.command.equals(upperCommand)) {
                    return type;
                }
            }
            return CUSTOM;
        }
    }
    
    /**
     * 构造函数
     */
    public CommandMessage() {
        super(GT06Constants.PROTOCOL_COMMAND);
        this.needsConfirmation = false;
    }
    
    /**
     * 构造函数
     * 
     * @param commandContent 指令内容
     */
    public CommandMessage(String commandContent) {
        super(GT06Constants.PROTOCOL_COMMAND);
        this.commandContent = commandContent;
        parseCommand();
    }
    
    /**
     * 构造函数
     * 
     * @param commandType 指令类型
     * @param parameters 指令参数
     */
    public CommandMessage(CommandType commandType, String... parameters) {
        super(GT06Constants.PROTOCOL_COMMAND);
        this.commandType = commandType;
        this.parameters = parameters;
        this.needsConfirmation = commandType.needsConfirmation();
        buildCommandContent();
    }
    
    @Override
    public byte[] encode() {
        if (commandContent == null || commandContent.isEmpty()) {
            throw new IllegalArgumentException("Command content cannot be null or empty");
        }
        
        // 将指令内容转换为字节数组
        byte[] contentBytes = commandContent.getBytes(StandardCharsets.UTF_8);
        
        // 创建包头
        byte[] header = createPacketHeader(1 + contentBytes.length);
        
        // 组装内容：指令长度(1) + 指令内容(N)
        byte[] content = ByteUtil.concat(
            new byte[]{(byte) contentBytes.length},
            contentBytes
        );
        
        // 组装完整数据包（不包含CRC和停止位）
        byte[] dataForCRC = ByteUtil.concat(header, content);
        
        // 创建包尾
        byte[] tail = createPacketTail(dataForCRC);
        
        // 返回完整数据包
        return ByteUtil.concat(dataForCRC, tail);
    }
    
    @Override
    public void decode(ByteBuf buffer) {
        // 读取包头
        readPacketHeader(buffer);
        
        // 读取指令长度
        int commandLength = buffer.readUnsignedByte();
        
        if (commandLength > 0) {
            // 读取指令内容
            byte[] contentBytes = new byte[commandLength];
            buffer.readBytes(contentBytes);
            this.commandContent = new String(contentBytes, StandardCharsets.UTF_8);
            
            // 解析指令
            parseCommand();
        }
        
        // 读取包尾
        readPacketTail(buffer);
    }
    
    /**
     * 解析指令内容
     */
    private void parseCommand() {
        if (commandContent == null || commandContent.trim().isEmpty()) {
            return;
        }
        
        String trimmedContent = commandContent.trim();
        
        // 分割指令和参数
        String[] parts = trimmedContent.split(",");
        if (parts.length > 0) {
            String command = parts[0].trim();
            this.commandType = CommandType.fromCommand(command);
            this.needsConfirmation = this.commandType.needsConfirmation();
            
            // 提取参数
            if (parts.length > 1) {
                this.parameters = new String[parts.length - 1];
                System.arraycopy(parts, 1, this.parameters, 0, parts.length - 1);
                
                // 清理参数中的空格
                for (int i = 0; i < this.parameters.length; i++) {
                    this.parameters[i] = this.parameters[i].trim();
                }
            }
        }
    }
    
    /**
     * 构建指令内容
     */
    private void buildCommandContent() {
        if (commandType == null) {
            return;
        }
        
        StringBuilder builder = new StringBuilder();
        builder.append(commandType.getCommand());
        
        if (parameters != null && parameters.length > 0) {
            for (String param : parameters) {
                builder.append(",").append(param);
            }
        }
        
        this.commandContent = builder.toString();
    }
    
    @Override
    public boolean validate() {
        // 验证协议号
        if (protocolNumber != GT06Constants.PROTOCOL_COMMAND) {
            return false;
        }
        
        // 验证指令内容
        if (commandContent == null || commandContent.trim().isEmpty()) {
            return false;
        }
        
        // 验证序列号
        if (serialNumber < 0 || serialNumber > 0xFFFF) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public String getMessageDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("COMMAND - ");
        
        if (commandType != null) {
            desc.append(commandType.getDescription());
            desc.append(" (").append(commandType.getCommand()).append(")");
        } else {
            desc.append("Unknown Command");
        }
        
        if (parameters != null && parameters.length > 0) {
            desc.append(" Params: ");
            desc.append(String.join(", ", parameters));
        }
        
        return desc.toString();
    }
    
    @Override
    public boolean needsResponse() {
        // 指令消息通常需要设备响应确认
        return needsConfirmation;
    }
    
    @Override
    public GT06Message createResponse() {
        if (!needsResponse()) {
            return null;
        }
        
        return ResponseMessage.createGenericResponse(this.protocolNumber, this.serialNumber);
    }
    
    /**
     * 创建位置请求指令
     * 
     * @return 位置请求指令消息
     */
    public static CommandMessage createLocationRequest() {
        return new CommandMessage(CommandType.LOCATION_REQUEST);
    }
    
    /**
     * 创建重启指令
     * 
     * @return 重启指令消息
     */
    public static CommandMessage createResetCommand() {
        return new CommandMessage(CommandType.RESET);
    }
    
    /**
     * 创建设置APN指令
     * 
     * @param apn APN名称
     * @param username 用户名
     * @param password 密码
     * @return APN设置指令消息
     */
    public static CommandMessage createApnSetCommand(String apn, String username, String password) {
        return new CommandMessage(CommandType.APN_SET, apn, username, password);
    }
    
    /**
     * 创建设置服务器指令
     * 
     * @param serverIp 服务器IP
     * @param serverPort 服务器端口
     * @return 服务器设置指令消息
     */
    public static CommandMessage createServerSetCommand(String serverIp, int serverPort) {
        return new CommandMessage(CommandType.SERVER_SET, serverIp, String.valueOf(serverPort));
    }
    
    /**
     * 创建设置上传间隔指令
     * 
     * @param intervalSeconds 上传间隔（秒）
     * @return 上传间隔设置指令消息
     */
    public static CommandMessage createUploadIntervalCommand(int intervalSeconds) {
        return new CommandMessage(CommandType.UPLOAD_INTERVAL, String.valueOf(intervalSeconds));
    }
    
    /**
     * 创建断油电指令
     * 
     * @param cut true为断开，false为恢复
     * @return 油电控制指令消息
     */
    public static CommandMessage createOilElectricityCommand(boolean cut) {
        CommandType type = cut ? CommandType.OIL_CUT : CommandType.OIL_RESUME;
        String param = cut ? "1" : "0";
        return new CommandMessage(type, param);
    }
    
    /**
     * 创建自定义指令
     * 
     * @param customCommand 自定义指令内容
     * @return 自定义指令消息
     */
    public static CommandMessage createCustomCommand(String customCommand) {
        CommandMessage message = new CommandMessage();
        message.commandContent = customCommand;
        message.commandType = CommandType.CUSTOM;
        message.needsConfirmation = true;
        return message;
    }
    
    /**
     * 获取指令的执行优先级
     * 
     * @return 优先级（数字越大优先级越高）
     */
    public int getPriority() {
        if (commandType == null) {
            return 0;
        }
        
        return switch (commandType) {
            case RESET, FACTORY_RESET -> 10;
            case OIL_CUT, OIL_RESUME -> 9;
            case ALARM_ARM, ALARM_DISARM -> 8;
            case GPS_ON, GPS_OFF -> 7;
            case LOCATION_REQUEST -> 6;
            case SERVER_SET, APN_SET -> 5;
            case UPLOAD_INTERVAL, HEARTBEAT_INTERVAL -> 4;
            case STATUS_QUERY, VERSION_QUERY -> 3;
            case MONITOR -> 2;
            default -> 1;
        };
    }
}
