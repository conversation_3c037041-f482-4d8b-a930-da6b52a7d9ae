package com.gt06.network.server;

import com.gt06.network.handler.GT06ChannelInitializer;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * GT06协议服务器
 * 基于Netty实现的高性能TCP服务器
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Component
public class GT06Server {

    @Value("${gt06.network.server.port:8888}")
    private int serverPort;

    @Value("${gt06.network.server.boss-threads:1}")
    private int bossThreads;

    @Value("${gt06.network.server.worker-threads:0}") // 0表示使用默认值
    private int workerThreads;

    @Value("${gt06.network.server.so-backlog:1024}")
    private int soBacklog;

    @Value("${gt06.network.server.so-keepalive:true}")
    private boolean soKeepAlive;

    @Value("${gt06.network.server.tcp-nodelay:true}")
    private boolean tcpNoDelay;

    @Value("${gt06.network.server.auto-start:true}")
    private boolean autoStart;

    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private ChannelFuture serverChannelFuture;
    private volatile boolean running = false;

    private final GT06ChannelInitializer channelInitializer;

    public GT06Server(GT06ChannelInitializer channelInitializer) {
        this.channelInitializer = channelInitializer;
    }

    /**
     * 初始化服务器配置
     */
    @PostConstruct
    public void init() {
        log.info("=== GT06 TCP Server Initialization ===");
        log.info("Server configuration:");
        log.info("  - Port: {}", serverPort);
        log.info("  - Boss threads: {}", bossThreads);
        log.info("  - Worker threads: {}", workerThreads > 0 ? workerThreads : "auto (" + (Runtime.getRuntime().availableProcessors() * 2) + ")");
        log.info("  - SO_BACKLOG: {}", soBacklog);
        log.info("  - SO_KEEPALIVE: {}", soKeepAlive);
        log.info("  - TCP_NODELAY: {}", tcpNoDelay);
        log.info("  - Auto start: {}", autoStart);
        log.info("GT06 TCP server configuration initialized");
    }

    /**
     * 应用启动完成后自动启动服务器
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        if (autoStart) {
            log.info("🚀 Application ready, starting GT06 TCP server...");
            try {
                startAsync();
            } catch (Exception e) {
                log.error("❌ Failed to auto-start GT06 TCP server: {}", e.getMessage(), e);
            }
        } else {
            log.info("⏸️ GT06 TCP server auto-start is disabled");
        }
    }

    /**
     * 启动服务器
     *
     * @return 启动结果的CompletableFuture
     */
    public CompletableFuture<Void> startAsync() {
        log.info("Starting GT06 TCP server asynchronously...");
        return CompletableFuture.runAsync(() -> {
            try {
                start();
            } catch (Exception e) {
                log.error("❌ Failed to start GT06 TCP server on port {}: {}", serverPort, e.getMessage(), e);
                throw new RuntimeException("Failed to start GT06 server", e);
            }
        });
    }

    /**
     * 同步启动服务器
     *
     * @throws InterruptedException 如果启动过程被中断
     */
    public void start() throws InterruptedException {
        if (running) {
            log.warn("⚠️ GT06 TCP server is already running on port {}", serverPort);
            return;
        }

        log.info("🚀 Starting GT06 TCP server on port {}...", serverPort);

        // 计算工作线程数
        int actualWorkerThreads = workerThreads > 0 ? workerThreads :
            Runtime.getRuntime().availableProcessors() * 2;

        log.info("📊 Creating event loop groups:");
        log.info("  - Boss group: {} threads", bossThreads);
        log.info("  - Worker group: {} threads", actualWorkerThreads);

        // 创建事件循环组
        bossGroup = new NioEventLoopGroup(bossThreads);
        workerGroup = new NioEventLoopGroup(actualWorkerThreads);

        try {
            log.info("🔧 Configuring server bootstrap...");
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(channelInitializer)
                    .option(ChannelOption.SO_BACKLOG, soBacklog)
                    .childOption(ChannelOption.SO_KEEPALIVE, soKeepAlive)
                    .childOption(ChannelOption.TCP_NODELAY, tcpNoDelay)
                    .childOption(ChannelOption.SO_REUSEADDR, true);

            log.info("🔌 Binding to port {}...", serverPort);
            // 绑定端口并启动服务器
            serverChannelFuture = bootstrap.bind(serverPort).sync();
            running = true;

            log.info("✅ GT06 TCP server started successfully!");
            log.info("📡 Server details:");
            log.info("  - Listening on port: {}", serverPort);
            log.info("  - Boss threads: {}", bossThreads);
            log.info("  - Worker threads: {}", actualWorkerThreads);
            log.info("  - Channel: {}", serverChannelFuture.channel().getClass().getSimpleName());
            log.info("  - Local address: {}", serverChannelFuture.channel().localAddress());
            log.info("🎯 GT06 TCP server is ready to accept device connections!");

            // 添加关闭钩子，在应用关闭时自动关闭服务器
            serverChannelFuture.channel().closeFuture().addListener(future -> {
                log.info("🛑 GT06 TCP server channel closed");
                shutdown();
            });

        } catch (Exception e) {
            log.error("❌ Failed to start GT06 TCP server: {}", e.getMessage(), e);
            shutdown();
            throw e;
        }
    }

    /**
     * 关闭服务器
     */
    @PreDestroy
    public void shutdown() {
        if (!running) {
            log.debug("GT06 TCP server is not running, skip shutdown");
            return;
        }

        log.info("🛑 Shutting down GT06 TCP server...");
        running = false;

        try {
            // 关闭服务器通道
            if (serverChannelFuture != null) {
                log.info("🔌 Closing server channel...");
                serverChannelFuture.channel().close().sync();
                log.info("✅ Server channel closed");
            }
        } catch (InterruptedException e) {
            log.warn("⚠️ Interrupted while closing server channel", e);
            Thread.currentThread().interrupt();
        }

        // 优雅关闭事件循环组
        log.info("🔄 Shutting down event loop groups...");
        if (bossGroup != null) {
            log.info("  - Shutting down boss group...");
            bossGroup.shutdownGracefully(2, 10, TimeUnit.SECONDS);
        }
        if (workerGroup != null) {
            log.info("  - Shutting down worker group...");
            workerGroup.shutdownGracefully(2, 10, TimeUnit.SECONDS);
        }

        log.info("✅ GT06 TCP server shutdown completed");
    }

    /**
     * 检查服务器是否正在运行
     *
     * @return 服务器运行状态
     */
    public boolean isRunning() {
        return running && serverChannelFuture != null && serverChannelFuture.channel().isActive();
    }

    /**
     * 获取服务器端口
     *
     * @return 服务器端口
     */
    public int getServerPort() {
        return serverPort;
    }

    /**
     * 获取当前连接数
     *
     * @return 当前连接数
     */
    public int getActiveConnections() {
        // 这里可以通过ChannelGroup或其他方式统计连接数
        // 暂时返回0，具体实现在ChannelHandler中
        return 0;
    }

    /**
     * 获取服务器统计信息
     *
     * @return 服务器统计信息
     */
    public ServerStats getServerStats() {
        return ServerStats.builder()
                .running(isRunning())
                .port(serverPort)
                .bossThreads(bossThreads)
                .workerThreads(workerThreads)
                .activeConnections(getActiveConnections())
                .build();
    }

    /**
     * 服务器统计信息
     */
    public static class ServerStats {
        private final boolean running;
        private final int port;
        private final int bossThreads;
        private final int workerThreads;
        private final int activeConnections;

        private ServerStats(boolean running, int port, int bossThreads,
                           int workerThreads, int activeConnections) {
            this.running = running;
            this.port = port;
            this.bossThreads = bossThreads;
            this.workerThreads = workerThreads;
            this.activeConnections = activeConnections;
        }

        public static ServerStatsBuilder builder() {
            return new ServerStatsBuilder();
        }

        // Getters
        public boolean isRunning() { return running; }
        public int getPort() { return port; }
        public int getBossThreads() { return bossThreads; }
        public int getWorkerThreads() { return workerThreads; }
        public int getActiveConnections() { return activeConnections; }

        @Override
        public String toString() {
            return String.format("ServerStats{running=%s, port=%d, bossThreads=%d, workerThreads=%d, activeConnections=%d}",
                running, port, bossThreads, workerThreads, activeConnections);
        }

        public static class ServerStatsBuilder {
            private boolean running;
            private int port;
            private int bossThreads;
            private int workerThreads;
            private int activeConnections;

            public ServerStatsBuilder running(boolean running) {
                this.running = running;
                return this;
            }

            public ServerStatsBuilder port(int port) {
                this.port = port;
                return this;
            }

            public ServerStatsBuilder bossThreads(int bossThreads) {
                this.bossThreads = bossThreads;
                return this;
            }

            public ServerStatsBuilder workerThreads(int workerThreads) {
                this.workerThreads = workerThreads;
                return this;
            }

            public ServerStatsBuilder activeConnections(int activeConnections) {
                this.activeConnections = activeConnections;
                return this;
            }

            public ServerStats build() {
                return new ServerStats(running, port, bossThreads, workerThreads, activeConnections);
            }
        }
    }
}
