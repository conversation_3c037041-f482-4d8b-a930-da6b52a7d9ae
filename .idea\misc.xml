<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisProjectProfileManager">
    <option name="PROJECT_PROFILE" />
    <option name="USE_PROJECT_LEVEL_SETTINGS" value="false" />
    <list size="0" />
  </component>
  <component name="CheckStyle-IDEA">
    <option name="configuration">
      <map>
        <entry key="checkstyle-version" value="8.16" />
        <entry key="copy-libs" value="true" />
        <entry key="location-0" value="BUNDLED:(bundled):Sun Checks" />
        <entry key="location-1" value="BUNDLED:(bundled):Google Checks" />
        <entry key="scan-before-checkin" value="false" />
        <entry key="scanscope" value="JavaOnly" />
        <entry key="suppress-errors" value="false" />
      </map>
    </option>
  </component>
  <component name="EasyCodeTableSetting">
    <option name="tableInfoMap">
      <map>
        <entry key="ycmain.act_evt_log">
          <value>
            <TableInfoDTO>
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map />
                    </option>
                    <option name="name" value="logNr_" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map />
                    </option>
                    <option name="name" value="type_" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map />
                    </option>
                    <option name="name" value="procDefId_" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map />
                    </option>
                    <option name="name" value="procInstId_" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map />
                    </option>
                    <option name="name" value="executionId_" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map />
                    </option>
                    <option name="name" value="taskId_" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map />
                    </option>
                    <option name="name" value="timeStamp_" />
                    <option name="type" value="java.lang.Object" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map />
                    </option>
                    <option name="name" value="userId_" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map />
                    </option>
                    <option name="name" value="data_" />
                    <option name="type" value="java.lang.Object" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map />
                    </option>
                    <option name="name" value="lockOwner_" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map />
                    </option>
                    <option name="name" value="lockTime_" />
                    <option name="type" value="java.lang.Object" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map />
                    </option>
                    <option name="name" value="isProcessed_" />
                    <option name="type" value="java.lang.Object" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="ActEvtLog" />
              <option name="preName" value="" />
              <option name="saveModelName" value="" />
              <option name="savePackageName" value="" />
              <option name="savePath" value="" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="JavaScriptSettings">
    <option name="languageLevel" value="ES6" />
  </component>
  <component name="Kotlin2JsCompilerArguments">
    <option name="sourceMapEmbedSources" value="inlining" />
  </component>
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State>
            <id />
          </State>
          <State>
            <id>Abstraction issuesJava</id>
          </State>
          <State>
            <id>Android</id>
          </State>
          <State>
            <id>Android &gt; Lint &gt; Correctness</id>
          </State>
          <State>
            <id>Android &gt; Lint &gt; Security</id>
          </State>
          <State>
            <id>Android Lint for Kotlin</id>
          </State>
          <State>
            <id>Code style issuesJava</id>
          </State>
          <State>
            <id>Error handlingJava</id>
          </State>
          <State>
            <id>General</id>
          </State>
          <State>
            <id>Google Web Toolkit issues</id>
          </State>
          <State>
            <id>Gradle</id>
          </State>
          <State>
            <id>Inheritance issuesJava</id>
          </State>
          <State>
            <id>Java</id>
          </State>
          <State>
            <id>Java language level migration aidsJava</id>
          </State>
          <State>
            <id>Javadoc issuesJava</id>
          </State>
          <State>
            <id>Kotlin</id>
          </State>
          <State>
            <id>Maven</id>
          </State>
          <State>
            <id>OSGi</id>
          </State>
          <State>
            <id>Probable bugsGradle</id>
          </State>
          <State>
            <id>Security issuesJava</id>
          </State>
          <State>
            <id>Serialization issuesJava</id>
          </State>
          <State>
            <id>Spring</id>
          </State>
          <State>
            <id>Spring SecuritySpring</id>
          </State>
          <State>
            <id>TestNGJava</id>
          </State>
        </expanded-state>
        <selected-state>
          <State>
            <id>SerializableHasSerialVersionUIDField</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_21" project-jdk-name="graalvm-21" project-jdk-type="JavaSDK" />
  <component name="SuppressionsComponent">
    <option name="suppComments" value="[]" />
  </component>
</project>