package com.gt06.service.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 位置数据实体类
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "gt06_location_data")
public class LocationData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 设备IMEI */
    @Column(name = "imei", length = 15, nullable = false)
    private String imei;

    /** 纬度 */
    @Column(name = "latitude", precision = 10, scale = 6, nullable = false)
    private BigDecimal latitude;

    /** 经度 */
    @Column(name = "longitude", precision = 10, scale = 6, nullable = false)
    private BigDecimal longitude;

    /** 海拔高度（米） */
    @Column(name = "altitude")
    private Integer altitude;

    /** 速度（km/h） */
    @Column(name = "speed")
    private Integer speed;

    /** 方向角（0-359度） */
    @Column(name = "direction")
    private Integer direction;

    /** GPS定位时间 */
    @Column(name = "location_time", nullable = false)
    private LocalDateTime locationTime;

    /** 是否GPS定位 */
    @Column(name = "gps_fixed", nullable = false)
    private Boolean gpsFixed = false;

    /** 卫星数量 */
    @Column(name = "satellite_count")
    private Integer satelliteCount;

    /** GPS精度（米） */
    @Column(name = "gps_accuracy")
    private Double gpsAccuracy;

    /** ACC状态 */
    @Column(name = "acc_on", nullable = false)
    private Boolean accOn = false;

    /** 里程数（公里） */
    @Column(name = "mileage", precision = 10, scale = 2)
    private BigDecimal mileage;

    /** 油量百分比 */
    @Column(name = "fuel_level")
    private Integer fuelLevel;

    /** 电池电压 */
    @Column(name = "battery_voltage", precision = 4, scale = 2)
    private BigDecimal batteryVoltage;

    /** 信号强度 */
    @Column(name = "signal_strength")
    private Integer signalStrength;

    /** MCC（移动国家代码） */
    @Column(name = "mcc")
    private Integer mcc;

    /** MNC（移动网络代码） */
    @Column(name = "mnc")
    private Integer mnc;

    /** LAC（位置区域代码） */
    @Column(name = "lac")
    private Integer lac;

    /** Cell ID（基站ID） */
    @Column(name = "cell_id")
    private Long cellId;

    /** 地址信息 */
    @Column(name = "address", length = 500)
    private String address;

    /** 省份 */
    @Column(name = "province", length = 50)
    private String province;

    /** 城市 */
    @Column(name = "city", length = 50)
    private String city;

    /** 区县 */
    @Column(name = "district", length = 50)
    private String district;

    /** 街道 */
    @Column(name = "street", length = 100)
    private String street;

    /** 位置类型 */
    @Enumerated(EnumType.STRING)
    @Column(name = "location_type", length = 20)
    private LocationType locationType = LocationType.GPS;

    /** 数据来源 */
    @Enumerated(EnumType.STRING)
    @Column(name = "data_source", length = 20)
    private DataSource dataSource = DataSource.DEVICE;

    /** 是否有效位置 */
    @Column(name = "valid_location", nullable = false)
    private Boolean validLocation = true;

    /** 原始数据 */
    @Column(name = "raw_data", length = 1000)
    private String rawData;

    /** 创建时间 */
    @CreationTimestamp
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    /**
     * 位置类型枚举
     */
    public enum LocationType {
        /** GPS定位 */
        GPS("GPS定位"),
        /** LBS定位 */
        LBS("LBS定位"),
        /** WIFI定位 */
        WIFI("WIFI定位"),
        /** 混合定位 */
        HYBRID("混合定位");

        private final String description;

        LocationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 数据来源枚举
     */
    public enum DataSource {
        /** 设备上报 */
        DEVICE("设备上报"),
        /** 平台查询 */
        PLATFORM("平台查询"),
        /** 第三方接口 */
        THIRD_PARTY("第三方接口");

        private final String description;

        DataSource(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查是否为有效的GPS坐标
     */
    public boolean isValidGpsCoordinate() {
        if (latitude == null || longitude == null) {
            return false;
        }

        double lat = latitude.doubleValue();
        double lng = longitude.doubleValue();

        return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180 &&
               !(lat == 0 && lng == 0);
    }

    /**
     * 获取坐标字符串
     */
    public String getCoordinateString() {
        if (latitude == null || longitude == null) {
            return "无坐标";
        }
        return String.format("%.6f,%.6f", latitude.doubleValue(), longitude.doubleValue());
    }

    /**
     * 计算与另一个位置的距离（米）
     */
    public double calculateDistance(LocationData other) {
        if (other == null || !this.isValidGpsCoordinate() || !other.isValidGpsCoordinate()) {
            return 0;
        }

        return calculateDistance(
            this.latitude.doubleValue(), this.longitude.doubleValue(),
            other.latitude.doubleValue(), other.longitude.doubleValue()
        );
    }

    /**
     * 使用Haversine公式计算两点间距离
     */
    private static double calculateDistance(double lat1, double lng1, double lat2, double lng2) {
        final double R = 6371000; // 地球半径（米）

        double dLat = Math.toRadians(lat2 - lat1);
        double dLng = Math.toRadians(lng2 - lng1);

        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                   Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                   Math.sin(dLng / 2) * Math.sin(dLng / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    }

    /**
     * 获取速度描述
     */
    public String getSpeedDescription() {
        if (speed == null) {
            return "未知";
        }

        if (speed == 0) {
            return "静止";
        } else if (speed <= 5) {
            return "缓慢";
        } else if (speed <= 40) {
            return "正常";
        } else if (speed <= 80) {
            return "较快";
        } else {
            return "高速";
        }
    }

    /**
     * 获取方向描述
     */
    public String getDirectionDescription() {
        if (direction == null) {
            return "未知";
        }

        String[] directions = {"北", "东北", "东", "东南", "南", "西南", "西", "西北"};
        int index = (int) Math.round(direction / 45.0) % 8;
        return directions[index];
    }
}
