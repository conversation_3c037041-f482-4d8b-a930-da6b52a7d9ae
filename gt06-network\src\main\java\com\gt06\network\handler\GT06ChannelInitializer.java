package com.gt06.network.handler;

import com.gt06.network.codec.GT06MessageDecoder;
import com.gt06.network.codec.GT06MessageEncoder;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * GT06协议通道初始化器
 * 配置Netty通道的处理器链
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Component
public class GT06ChannelInitializer extends ChannelInitializer<SocketChannel> {
    
    @Value("${gt06.network.server.read-timeout:300}")
    private int readTimeoutSeconds;

    @Value("${gt06.network.server.write-timeout:60}")
    private int writeTimeoutSeconds;

    @Value("${gt06.network.server.all-timeout:360}")
    private int allTimeoutSeconds;

    @Value("${gt06.network.server.enable-logging:true}")
    private boolean enableLogging;

    @Value("${gt06.network.server.log-level:DEBUG}")
    private String logLevel;
    
    private final GT06MessageDecoder messageDecoder;
    private final GT06MessageEncoder messageEncoder;
    private final GT06MessageHandler messageHandler;
    private final ConnectionHandler connectionHandler;
    
    public GT06ChannelInitializer(GT06MessageDecoder messageDecoder,
                                 GT06MessageEncoder messageEncoder,
                                 GT06MessageHandler messageHandler,
                                 ConnectionHandler connectionHandler) {
        this.messageDecoder = messageDecoder;
        this.messageEncoder = messageEncoder;
        this.messageHandler = messageHandler;
        this.connectionHandler = connectionHandler;

        log.info("🔧 GT06ChannelInitializer created with handlers:");
        log.info("  - Message Decoder: {}", messageDecoder.getClass().getSimpleName());
        log.info("  - Message Encoder: {}", messageEncoder.getClass().getSimpleName());
        log.info("  - Message Handler: {}", messageHandler.getClass().getSimpleName());
        log.info("  - Connection Handler: {}", connectionHandler.getClass().getSimpleName());
    }
    
    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        log.info("🔗 Initializing channel pipeline for new connection: {}", ch.remoteAddress());
        ChannelPipeline pipeline = ch.pipeline();

        // 1. 空闲状态检测处理器
        log.debug("  ➕ Adding IdleStateHandler (read:{}, write:{}, all:{})",
            readTimeoutSeconds, writeTimeoutSeconds, allTimeoutSeconds);
        pipeline.addLast("idleStateHandler", new IdleStateHandler(
            readTimeoutSeconds,   // 读超时
            writeTimeoutSeconds,  // 写超时
            allTimeoutSeconds,    // 读写超时
            TimeUnit.SECONDS
        ));

        // 2. 连接管理处理器
        log.debug("  ➕ Adding ConnectionHandler");
        pipeline.addLast("connectionHandler", connectionHandler);

        // 3. 日志处理器（可选）
        if (enableLogging) {
            LogLevel nettyLogLevel = parseLogLevel(logLevel);
            log.debug("  ➕ Adding LoggingHandler (level: {})", nettyLogLevel);
            pipeline.addLast("loggingHandler", new LoggingHandler(nettyLogLevel));
        } else {
            log.debug("  ⏭️ Skipping LoggingHandler (disabled)");
        }
        
        // 4. GT06协议解码器
        log.debug("  ➕ Adding GT06MessageDecoder");
        pipeline.addLast("gt06Decoder", messageDecoder);

        // 5. GT06协议编码器
        log.debug("  ➕ Adding GT06MessageEncoder");
        pipeline.addLast("gt06Encoder", messageEncoder);

        // 6. GT06消息处理器
        log.debug("  ➕ Adding GT06MessageHandler");
        pipeline.addLast("gt06MessageHandler", messageHandler);

        log.info("✅ Channel pipeline initialized successfully for {}", ch.remoteAddress());
        log.debug("📋 Pipeline handlers: idleStateHandler -> connectionHandler -> {} -> gt06Decoder -> gt06Encoder -> gt06MessageHandler",
            enableLogging ? "loggingHandler" : "(no logging)");
    }
    
    /**
     * 解析日志级别
     * 
     * @param level 日志级别字符串
     * @return Netty日志级别
     */
    private LogLevel parseLogLevel(String level) {
        if (level == null) {
            return LogLevel.INFO;
        }
        
        switch (level.toUpperCase()) {
            case "TRACE": return LogLevel.TRACE;
            case "DEBUG": return LogLevel.DEBUG;
            case "INFO": return LogLevel.INFO;
            case "WARN": return LogLevel.WARN;
            case "ERROR": return LogLevel.ERROR;
            default:
                log.warn("Unknown log level: {}, using INFO", level);
                return LogLevel.INFO;
        }
    }
    
    /**
     * 获取超时配置信息
     * 
     * @return 超时配置描述
     */
    public String getTimeoutConfig() {
        return String.format("Timeouts: read=%ds, write=%ds, all=%ds", 
            readTimeoutSeconds, writeTimeoutSeconds, allTimeoutSeconds);
    }
    
    /**
     * 检查超时配置是否合理
     * 
     * @return 配置是否合理
     */
    public boolean isTimeoutConfigValid() {
        return readTimeoutSeconds > 0 && 
               writeTimeoutSeconds > 0 && 
               allTimeoutSeconds > 0 &&
               allTimeoutSeconds >= Math.max(readTimeoutSeconds, writeTimeoutSeconds);
    }
    
    /**
     * 获取处理器链配置信息
     * 
     * @return 处理器链描述
     */
    public String getPipelineConfig() {
        StringBuilder config = new StringBuilder();
        config.append("Pipeline: ");
        config.append("IdleStateHandler -> ");
        config.append("ConnectionHandler -> ");
        
        if (enableLogging) {
            config.append("LoggingHandler(").append(logLevel).append(") -> ");
        }
        
        config.append("GT06Decoder -> ");
        config.append("GT06Encoder -> ");
        config.append("GT06MessageHandler");
        
        return config.toString();
    }
}
