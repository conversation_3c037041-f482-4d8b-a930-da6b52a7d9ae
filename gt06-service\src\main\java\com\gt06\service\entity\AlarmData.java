package com.gt06.service.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 报警数据实体类
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "gt06_alarm_data", indexes = {
    @Index(name = "idx_alarm_imei", columnList = "imei"),
    @Index(name = "idx_alarm_type", columnList = "alarmType"),
    @Index(name = "idx_alarm_level", columnList = "alarmLevel"),
    @Index(name = "idx_alarm_status", columnList = "status"),
    @Index(name = "idx_alarm_time", columnList = "alarmTime"),
    @Index(name = "idx_alarm_create_time", columnList = "createTime")
})
public class AlarmData {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /** 设备IMEI */
    @Column(name = "imei", length = 15, nullable = false)
    private String imei;
    
    /** 报警类型 */
    @Enumerated(EnumType.STRING)
    @Column(name = "alarm_type", length = 30, nullable = false)
    private AlarmType alarmType;
    
    /** 报警级别 */
    @Enumerated(EnumType.STRING)
    @Column(name = "alarm_level", length = 20, nullable = false)
    private AlarmLevel alarmLevel;
    
    /** 报警时间 */
    @Column(name = "alarm_time", nullable = false)
    private LocalDateTime alarmTime;
    
    /** 报警描述 */
    @Column(name = "alarm_description", length = 200)
    private String alarmDescription;
    
    /** 报警状态 */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20, nullable = false)
    private AlarmStatus status = AlarmStatus.PENDING;
    
    /** 纬度 */
    @Column(name = "latitude", precision = 10, scale = 6)
    private BigDecimal latitude;
    
    /** 经度 */
    @Column(name = "longitude", precision = 10, scale = 6)
    private BigDecimal longitude;
    
    /** 地址信息 */
    @Column(name = "address", length = 500)
    private String address;
    
    /** 速度 */
    @Column(name = "speed")
    private Integer speed;
    
    /** 方向 */
    @Column(name = "direction")
    private Integer direction;
    
    /** 是否GPS定位 */
    @Column(name = "gps_fixed")
    private Boolean gpsFixed;
    
    /** 处理人员 */
    @Column(name = "handler", length = 50)
    private String handler;
    
    /** 处理时间 */
    @Column(name = "handle_time")
    private LocalDateTime handleTime;
    
    /** 处理备注 */
    @Column(name = "handle_remarks", length = 500)
    private String handleRemarks;
    
    /** 确认人员 */
    @Column(name = "confirmer", length = 50)
    private String confirmer;
    
    /** 确认时间 */
    @Column(name = "confirm_time")
    private LocalDateTime confirmTime;
    
    /** 是否已通知 */
    @Column(name = "notified", nullable = false)
    private Boolean notified = false;
    
    /** 通知时间 */
    @Column(name = "notify_time")
    private LocalDateTime notifyTime;
    
    /** 通知方式 */
    @Column(name = "notify_method", length = 100)
    private String notifyMethod;
    
    /** 原始数据 */
    @Column(name = "raw_data", length = 1000)
    private String rawData;
    
    /** 创建时间 */
    @CreationTimestamp
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;
    
    /** 更新时间 */
    @UpdateTimestamp
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
    
    /**
     * 报警类型枚举
     */
    public enum AlarmType {
        NORMAL("正常", AlarmLevel.INFO),
        SOS("SOS紧急报警", AlarmLevel.CRITICAL),
        POWER_OFF("断电报警", AlarmLevel.HIGH),
        VIBRATION("震动报警", AlarmLevel.MEDIUM),
        FENCE_IN("进围栏报警", AlarmLevel.MEDIUM),
        FENCE_OUT("出围栏报警", AlarmLevel.MEDIUM),
        OVERSPEED("超速报警", AlarmLevel.HIGH),
        TAMPER("防拆报警", AlarmLevel.HIGH),
        RAPID_ACCELERATION("急加速报警", AlarmLevel.MEDIUM),
        RAPID_DECELERATION("急减速报警", AlarmLevel.MEDIUM),
        SHARP_TURN("急转弯报警", AlarmLevel.MEDIUM),
        COLLISION("碰撞报警", AlarmLevel.CRITICAL),
        LOW_BATTERY("低电量报警", AlarmLevel.LOW),
        GPS_ANTENNA_CUT("GPS天线断开", AlarmLevel.HIGH),
        GPS_ANTENNA_SHORT("GPS天线短路", AlarmLevel.HIGH),
        POWER_CUT("主电源断开", AlarmLevel.HIGH),
        ILLEGAL_IGNITION("非法点火", AlarmLevel.HIGH),
        ILLEGAL_DISPLACEMENT("非法位移", AlarmLevel.HIGH);
        
        private final String description;
        private final AlarmLevel defaultLevel;
        
        AlarmType(String description, AlarmLevel defaultLevel) {
            this.description = description;
            this.defaultLevel = defaultLevel;
        }
        
        public String getDescription() { return description; }
        public AlarmLevel getDefaultLevel() { return defaultLevel; }
    }
    
    /**
     * 报警级别枚举
     */
    public enum AlarmLevel {
        INFO(0, "信息", "#17a2b8"),
        LOW(1, "低级", "#28a745"),
        MEDIUM(2, "中级", "#ffc107"),
        HIGH(3, "高级", "#fd7e14"),
        CRITICAL(4, "紧急", "#dc3545");
        
        private final int level;
        private final String description;
        private final String color;
        
        AlarmLevel(int level, String description, String color) {
            this.level = level;
            this.description = description;
            this.color = color;
        }
        
        public int getLevel() { return level; }
        public String getDescription() { return description; }
        public String getColor() { return color; }
    }
    
    /**
     * 报警状态枚举
     */
    public enum AlarmStatus {
        PENDING("待处理", "#ffc107"),
        PROCESSING("处理中", "#17a2b8"),
        RESOLVED("已解决", "#28a745"),
        IGNORED("已忽略", "#6c757d"),
        ESCALATED("已升级", "#dc3545");
        
        private final String description;
        private final String color;
        
        AlarmStatus(String description, String color) {
            this.description = description;
            this.color = color;
        }
        
        public String getDescription() { return description; }
        public String getColor() { return color; }
    }
    
    /**
     * 检查是否为紧急报警
     */
    public boolean isCritical() {
        return alarmLevel == AlarmLevel.CRITICAL;
    }
    
    /**
     * 检查是否需要立即处理
     */
    public boolean needsImmediateAction() {
        return alarmLevel.getLevel() >= AlarmLevel.HIGH.getLevel();
    }
    
    /**
     * 检查报警是否已处理
     */
    public boolean isProcessed() {
        return status == AlarmStatus.RESOLVED || status == AlarmStatus.IGNORED;
    }
    
    /**
     * 获取报警持续时间（分钟）
     */
    public long getDurationMinutes() {
        LocalDateTime endTime = isProcessed() ? 
            (handleTime != null ? handleTime : updateTime) : LocalDateTime.now();
        return java.time.Duration.between(alarmTime, endTime).toMinutes();
    }
    
    /**
     * 获取位置描述
     */
    public String getLocationDescription() {
        if (latitude != null && longitude != null && 
            latitude.compareTo(BigDecimal.ZERO) != 0 && longitude.compareTo(BigDecimal.ZERO) != 0) {
            return String.format("GPS: %.6f, %.6f", latitude.doubleValue(), longitude.doubleValue());
        } else if (address != null && !address.trim().isEmpty()) {
            return address;
        } else {
            return "位置信息不可用";
        }
    }
    
    /**
     * 处理报警
     */
    public void handle(String handler, String remarks) {
        this.status = AlarmStatus.PROCESSING;
        this.handler = handler;
        this.handleTime = LocalDateTime.now();
        this.handleRemarks = remarks;
    }
    
    /**
     * 解决报警
     */
    public void resolve(String handler, String remarks) {
        this.status = AlarmStatus.RESOLVED;
        this.handler = handler;
        this.handleTime = LocalDateTime.now();
        this.handleRemarks = remarks;
    }
    
    /**
     * 忽略报警
     */
    public void ignore(String handler, String remarks) {
        this.status = AlarmStatus.IGNORED;
        this.handler = handler;
        this.handleTime = LocalDateTime.now();
        this.handleRemarks = remarks;
    }
    
    /**
     * 升级报警
     */
    public void escalate(String handler, String remarks) {
        this.status = AlarmStatus.ESCALATED;
        this.handler = handler;
        this.handleTime = LocalDateTime.now();
        this.handleRemarks = remarks;
        
        // 升级报警级别
        if (this.alarmLevel.getLevel() < AlarmLevel.CRITICAL.getLevel()) {
            AlarmLevel[] levels = AlarmLevel.values();
            for (int i = 0; i < levels.length; i++) {
                if (levels[i] == this.alarmLevel && i < levels.length - 1) {
                    this.alarmLevel = levels[i + 1];
                    break;
                }
            }
        }
    }
    
    /**
     * 标记已通知
     */
    public void markNotified(String method) {
        this.notified = true;
        this.notifyTime = LocalDateTime.now();
        this.notifyMethod = method;
    }
}
