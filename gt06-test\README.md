# GT06 TCP服务器测试套件

## 📋 概述

GT06测试模块提供了完整的TCP服务器测试功能，包括：
- **集成测试** - 验证服务器基本功能
- **协议测试** - 验证GT06协议实现
- **性能测试** - 验证服务器性能表现
- **客户端工具** - 交互式测试工具

## 🧪 测试类型

### 1. 集成测试 (GT06ServerIntegrationTest)
- ✅ TCP连接建立
- ✅ 设备登录流程
- ✅ 位置数据上报
- ✅ 心跳机制
- ✅ 多客户端并发连接
- ✅ 连接断开和重连

### 2. 协议测试 (GT06ProtocolTest)
- ✅ 登录消息格式验证
- ✅ 位置消息格式验证
- ✅ 心跳消息格式验证
- ✅ 消息序列号机制
- ✅ 异常消息处理
- ✅ 大数据量传输
- ✅ 连接超时机制
- ✅ 协议版本兼容性

### 3. 性能测试 (GT06PerformanceTest)
- 🚀 高并发连接测试 (100个并发客户端)
- 🔄 持续负载测试 (50个客户端持续1分钟)
- 💾 内存使用测试 (200个连接的内存占用)

## 🛠️ 使用方法

### 前置条件
1. **GT06 TCP服务器必须运行在 localhost:8888**
2. **数据库连接可用**
3. **Redis连接可用**
4. **足够的系统资源用于性能测试**

### 运行单个测试类
```bash
# 运行集成测试
mvn test -Dtest=GT06ServerIntegrationTest

# 运行协议测试
mvn test -Dtest=GT06ProtocolTest

# 运行性能测试
mvn test -Dtest=GT06PerformanceTest
```

### 运行完整测试套件
```bash
# 运行所有测试
mvn test -Dtest=GT06TestSuite
```

### 使用交互式客户端工具
```bash
# 编译项目
mvn compile

# 运行客户端工具
java -cp target/classes com.gt06.test.GT06ClientTool [host] [port] [deviceId]

# 示例
java -cp target/classes com.gt06.test.GT06ClientTool localhost 8888 TEST_001
```

## 🎯 测试客户端功能

### GT06TestClient 类
模拟GPS设备的完整功能：

```java
// 创建测试客户端
GT06TestClient client = new GT06TestClient("localhost", 8888, "DEVICE_001");

// 连接到服务器
boolean connected = client.connect();

// 发送登录消息
boolean loginSuccess = client.sendLogin();

// 发送位置数据
boolean locationSuccess = client.sendLocation(39.9042, 116.4074, 60);

// 发送心跳
boolean heartbeatSuccess = client.sendHeartbeat();

// 启动自动心跳
client.startHeartbeat(30); // 每30秒一次

// 断开连接
client.disconnect();
```

### 交互式客户端工具命令

```
📋 Available Commands:
  1. connect    - Connect to GT06 server
  2. login      - Send login message
  3. location   - Send location message
  4. heartbeat  - Send heartbeat message
  5. auto       - Start automatic heartbeat
  6. status     - Show connection status
  7. test       - Run quick test sequence
  8. stress     - Run stress test
  9. disconnect - Disconnect from server
  0. exit       - Exit the tool
```

## 📊 预期测试结果

### 性能指标
- **连接成功率**: >= 90%
- **消息成功率**: >= 95%
- **响应时间**: < 5秒
- **内存使用**: < 500MB (200个连接)
- **吞吐量**: >= 10 消息/秒

### 测试覆盖范围
- ✅ 基本TCP连接
- ✅ GT06协议消息格式
- ✅ 设备认证和会话管理
- ✅ 实时位置数据处理
- ✅ 心跳保活机制
- ✅ 并发连接处理
- ✅ 异常情况处理
- ✅ 内存和性能表现

## 🔧 配置说明

### 测试配置文件
测试使用 `application-test.yml` 配置文件，确保：
- 数据库连接正确
- Redis连接正确
- GT06服务器端口配置正确

### 自定义测试参数
可以通过系统属性自定义测试参数：

```bash
# 自定义服务器地址和端口
mvn test -Dtest.server.host=************* -Dtest.server.port=9999

# 自定义并发数量
mvn test -Dtest.concurrent.clients=50

# 自定义测试持续时间
mvn test -Dtest.duration.seconds=120
```

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查GT06服务器是否运行
   - 检查端口是否正确 (默认8888)
   - 检查防火墙设置

2. **测试超时**
   - 增加测试超时时间
   - 检查服务器性能
   - 减少并发客户端数量

3. **内存不足**
   - 增加JVM堆内存: `-Xmx2g`
   - 减少性能测试的客户端数量

4. **数据库连接错误**
   - 检查数据库服务状态
   - 验证连接配置
   - 确保测试数据库存在

### 日志调试
启用详细日志：
```bash
mvn test -Dlogging.level.com.gt06=DEBUG
```

## 📈 测试报告

测试完成后，查看详细的测试报告：
- 控制台输出包含实时测试结果
- 性能测试会输出详细的统计信息
- 失败的测试会显示具体错误信息

## 🚀 持续集成

在CI/CD流水线中集成测试：

```yaml
# GitHub Actions 示例
- name: Run GT06 Tests
  run: |
    # 启动GT06服务器
    java -jar gt06-web/target/gt06-web-1.0.0.jar &
    sleep 30
    
    # 运行测试
    mvn test -Dtest=GT06TestSuite
    
    # 停止服务器
    pkill -f gt06-web
```

## 📞 支持

如果遇到问题或需要帮助：
1. 查看测试日志输出
2. 检查服务器状态和配置
3. 验证网络连接
4. 查看系统资源使用情况
