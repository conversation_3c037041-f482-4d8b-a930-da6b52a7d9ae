package com.gt06.test.performance;

import com.gt06.test.client.GT06TestClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GT06服务器性能测试
 * 测试服务器在高并发情况下的性能表现
 * 
 * <AUTHOR> Test
 * @version 1.0.0
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class GT06PerformanceTest {
    
    private static final String TEST_HOST = "localhost";
    private static final int TEST_PORT = 8888;
    
    /**
     * 测试高并发连接
     */
    @Test
    @DisplayName("高并发连接测试")
    void testHighConcurrentConnections() {
        log.info("🚀 Starting high concurrent connections test...");
        
        int clientCount = 100;
        int threadPoolSize = 20;
        ExecutorService executor = Executors.newFixedThreadPool(threadPoolSize);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        AtomicLong totalResponseTime = new AtomicLong(0);
        
        List<Future<Long>> futures = new ArrayList<>();
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 提交并发任务
            for (int i = 0; i < clientCount; i++) {
                final int clientId = i;
                Future<Long> future = executor.submit(() -> {
                    String deviceId = "PERF_TEST_" + String.format("%03d", clientId);
                    GT06TestClient client = new GT06TestClient(TEST_HOST, TEST_PORT, deviceId);
                    
                    long taskStartTime = System.currentTimeMillis();
                    
                    try {
                        // 连接
                        if (!client.connect()) {
                            failureCount.incrementAndGet();
                            return -1L;
                        }
                        
                        // 登录
                        if (!client.sendLogin()) {
                            failureCount.incrementAndGet();
                            return -1L;
                        }
                        
                        // 发送位置数据
                        if (!client.sendLocation(39.9042 + Math.random() * 0.1, 
                                                116.4074 + Math.random() * 0.1, 
                                                (int)(Math.random() * 120))) {
                            failureCount.incrementAndGet();
                            return -1L;
                        }
                        
                        // 发送心跳
                        if (!client.sendHeartbeat()) {
                            failureCount.incrementAndGet();
                            return -1L;
                        }
                        
                        successCount.incrementAndGet();
                        long responseTime = System.currentTimeMillis() - taskStartTime;
                        totalResponseTime.addAndGet(responseTime);
                        
                        return responseTime;
                        
                    } catch (Exception e) {
                        log.error("Client {} failed: {}", deviceId, e.getMessage());
                        failureCount.incrementAndGet();
                        return -1L;
                    } finally {
                        client.disconnect();
                    }
                });
                
                futures.add(future);
            }
            
            // 等待所有任务完成
            for (Future<Long> future : futures) {
                try {
                    future.get(30, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.error("Task execution failed: {}", e.getMessage());
                    failureCount.incrementAndGet();
                }
            }
            
            long totalTime = System.currentTimeMillis() - startTime;
            
            // 计算统计信息
            int successful = successCount.get();
            int failed = failureCount.get();
            double successRate = (double) successful / clientCount * 100;
            double avgResponseTime = successful > 0 ? (double) totalResponseTime.get() / successful : 0;
            double throughput = (double) successful / totalTime * 1000; // 每秒处理数
            
            // 输出测试结果
            log.info("📊 High Concurrent Connections Test Results:");
            log.info("  - Total clients: {}", clientCount);
            log.info("  - Successful: {}", successful);
            log.info("  - Failed: {}", failed);
            log.info("  - Success rate: {:.2f}%", successRate);
            log.info("  - Average response time: {:.2f}ms", avgResponseTime);
            log.info("  - Total test time: {}ms", totalTime);
            log.info("  - Throughput: {:.2f} requests/second", throughput);
            
            // 断言
            assertTrue(successRate >= 90.0, "Success rate should be at least 90%");
            assertTrue(avgResponseTime < 5000, "Average response time should be less than 5 seconds");
            
        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("✅ High concurrent connections test completed");
    }
    
    /**
     * 测试持续负载
     */
    @Test
    @DisplayName("持续负载测试")
    void testSustainedLoad() {
        log.info("🔄 Starting sustained load test...");
        
        int clientCount = 50;
        int testDurationSeconds = 60; // 1分钟测试
        int messageIntervalMs = 1000; // 每秒发送一条消息
        
        ExecutorService executor = Executors.newFixedThreadPool(clientCount);
        AtomicInteger totalMessages = new AtomicInteger(0);
        AtomicInteger successfulMessages = new AtomicInteger(0);
        AtomicInteger failedMessages = new AtomicInteger(0);
        
        List<Future<Void>> futures = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        
        try {
            // 启动持续负载客户端
            for (int i = 0; i < clientCount; i++) {
                final int clientId = i;
                Future<Void> future = executor.submit(() -> {
                    String deviceId = "LOAD_TEST_" + String.format("%03d", clientId);
                    GT06TestClient client = new GT06TestClient(TEST_HOST, TEST_PORT, deviceId);
                    
                    try {
                        // 连接并登录
                        if (!client.connect() || !client.sendLogin()) {
                            log.error("Client {} failed to connect or login", deviceId);
                            return null;
                        }
                        
                        long endTime = startTime + testDurationSeconds * 1000L;
                        int messageCount = 0;
                        
                        while (System.currentTimeMillis() < endTime) {
                            totalMessages.incrementAndGet();
                            
                            try {
                                // 交替发送位置和心跳消息
                                boolean success;
                                if (messageCount % 3 == 0) {
                                    success = client.sendHeartbeat();
                                } else {
                                    success = client.sendLocation(
                                        39.9042 + Math.random() * 0.01,
                                        116.4074 + Math.random() * 0.01,
                                        (int)(Math.random() * 100)
                                    );
                                }
                                
                                if (success) {
                                    successfulMessages.incrementAndGet();
                                } else {
                                    failedMessages.incrementAndGet();
                                }
                                
                                messageCount++;
                                Thread.sleep(messageIntervalMs);
                                
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                break;
                            } catch (Exception e) {
                                failedMessages.incrementAndGet();
                                log.debug("Message send failed for {}: {}", deviceId, e.getMessage());
                            }
                        }
                        
                    } catch (Exception e) {
                        log.error("Client {} error: {}", deviceId, e.getMessage());
                    } finally {
                        client.disconnect();
                    }
                    
                    return null;
                });
                
                futures.add(future);
            }
            
            // 等待测试完成
            for (Future<Void> future : futures) {
                try {
                    future.get(testDurationSeconds + 30, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.error("Sustained load task failed: {}", e.getMessage());
                }
            }
            
            long actualDuration = System.currentTimeMillis() - startTime;
            
            // 计算统计信息
            int total = totalMessages.get();
            int successful = successfulMessages.get();
            int failed = failedMessages.get();
            double successRate = total > 0 ? (double) successful / total * 100 : 0;
            double messageRate = (double) total / actualDuration * 1000; // 每秒消息数
            
            // 输出测试结果
            log.info("📊 Sustained Load Test Results:");
            log.info("  - Test duration: {}ms ({} seconds)", actualDuration, actualDuration / 1000);
            log.info("  - Concurrent clients: {}", clientCount);
            log.info("  - Total messages: {}", total);
            log.info("  - Successful messages: {}", successful);
            log.info("  - Failed messages: {}", failed);
            log.info("  - Success rate: {:.2f}%", successRate);
            log.info("  - Message rate: {:.2f} messages/second", messageRate);
            log.info("  - Average messages per client: {:.2f}", (double) total / clientCount);
            
            // 断言
            assertTrue(successRate >= 95.0, "Success rate should be at least 95% for sustained load");
            assertTrue(messageRate >= clientCount * 0.8, "Message rate should be reasonable");
            
        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("✅ Sustained load test completed");
    }
    
    /**
     * 测试内存使用情况
     */
    @Test
    @DisplayName("内存使用测试")
    void testMemoryUsage() {
        log.info("💾 Starting memory usage test...");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 记录初始内存使用
        System.gc(); // 建议进行垃圾回收
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        log.info("Initial memory usage: {} MB", initialMemory / 1024 / 1024);
        
        // 创建大量连接
        int clientCount = 200;
        List<GT06TestClient> clients = new ArrayList<>();
        
        try {
            for (int i = 0; i < clientCount; i++) {
                String deviceId = "MEM_TEST_" + String.format("%03d", i);
                GT06TestClient client = new GT06TestClient(TEST_HOST, TEST_PORT, deviceId);
                
                if (client.connect()) {
                    client.sendLogin();
                    clients.add(client);
                }
                
                // 每50个连接检查一次内存
                if ((i + 1) % 50 == 0) {
                    System.gc();
                    long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                    log.info("Memory usage after {} connections: {} MB", 
                        i + 1, currentMemory / 1024 / 1024);
                }
            }
            
            // 记录峰值内存使用
            System.gc();
            long peakMemory = runtime.totalMemory() - runtime.freeMemory();
            long memoryIncrease = peakMemory - initialMemory;
            
            log.info("📊 Memory Usage Test Results:");
            log.info("  - Initial memory: {} MB", initialMemory / 1024 / 1024);
            log.info("  - Peak memory: {} MB", peakMemory / 1024 / 1024);
            log.info("  - Memory increase: {} MB", memoryIncrease / 1024 / 1024);
            log.info("  - Active connections: {}", clients.size());
            log.info("  - Memory per connection: {} KB", 
                clients.size() > 0 ? memoryIncrease / clients.size() / 1024 : 0);
            
            // 断言内存使用合理
            assertTrue(memoryIncrease < 500 * 1024 * 1024, // 500MB
                "Memory increase should be less than 500MB");
            
        } finally {
            // 清理所有连接
            for (GT06TestClient client : clients) {
                client.disconnect();
            }
            
            // 等待清理完成
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            System.gc();
            long finalMemory = runtime.totalMemory() - runtime.freeMemory();
            log.info("Final memory usage: {} MB", finalMemory / 1024 / 1024);
        }
        
        log.info("✅ Memory usage test completed");
    }
}
