package com.gt06.web.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;

/**
 * 启动健康检查
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StartupHealthCheck implements ApplicationRunner {
    
    private final DataSource dataSource;
    private final RedissonClient redissonClient;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始启动健康检查...");
        
        // 检查数据库连接
        try (Connection connection = dataSource.getConnection()) {
            log.info("✅ 数据库连接正常: {}", connection.getMetaData().getURL());
        } catch (Exception e) {
            log.error("❌ 数据库连接失败", e);
        }
        
        // 检查Redis连接
        try {
            redissonClient.getBucket("health-check").set("ok");
            String result = redissonClient.getBucket("health-check").get().toString();
            log.info("✅ Redis连接正常: {}", result);
            redissonClient.getBucket("health-check").delete();
        } catch (Exception e) {
            log.error("❌ Redis连接失败", e);
        }
        
        log.info("启动健康检查完成");
    }
}
