# R16-E动态物联网SP(GT06)协议系统设计文档

## 1. 项目概述

### 1.1 项目背景
基于R16-E动态物联网SP(GT06)中文协议1.02版本，开发一套完整的车载GPS定位器与服务器平台通信系统。系统采用JDK21开发，支持多种设备类型的数据接收、处理和响应。

### 1.2 技术栈
- **JDK版本**: OpenJDK 21
- **网络框架**: Netty 4.x
- **数据库**: MySQL 8.0 / PostgreSQL 14+
- **缓存**: Redis 7.x
- **消息队列**: RabbitMQ / Apache Kafka
- **框架**: Spring Boot 3.x
- **构建工具**: Maven 3.9+

### 1.3 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GPS设备终端    │────│   Netty服务器    │────│   业务处理层     │
│   (GT06协议)    │    │   (协议解析)     │    │   (数据处理)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                │                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   数据存储层     │    │   外部接口层     │
                       │ (MySQL/Redis)   │    │   (REST API)    │
                       └─────────────────┘    └─────────────────┘
```

## 2. 协议分析

### 2.1 数据包格式
GT06协议采用固定格式的二进制数据包：

| 字段 | 长度(Byte) | 说明 |
|------|------------|------|
| 起始位 | 2 | 0x78 0x78 |
| 包长度 | 1 | 协议号+信息内容+信息序列号+错误校验 |
| 协议号 | 1 | 标识数据包类型 |
| 信息内容 | N | 变长数据内容 |
| 信息序列号 | 2 | 递增序列号 |
| 错误校验 | 2 | CRC-ITU校验 |
| 停止位 | 2 | 0x0D 0x0A |

### 2.2 主要协议类型

| 协议号 | 名称 | 方向 | 说明 |
|--------|------|------|------|
| 0x01 | 登录包 | 终端→服务器 | 设备登录认证 |
| 0x12 | 定位数据包 | 终端→服务器 | GPS/LBS位置信息 |
| 0x13 | 心跳包 | 双向 | 保持连接活跃 |
| 0x15 | 字符串信息 | 终端→服务器 | 指令响应 |
| 0x16 | 报警信息 | 终端→服务器 | 各类报警数据 |
| 0x80 | 下发指令 | 服务器→终端 | 服务器指令 |
| 0x90 | IMSI上报 | 终端→服务器 | SIM卡信息 |
| 0x94 | ICCID上报 | 终端→服务器 | SIM卡标识 |

## 3. 系统设计

### 3.1 核心模块设计

#### 3.1.1 网络通信模块 (gt06-network)
```java
// Netty服务器配置
@Component
public class GT06Server {
    private final EventLoopGroup bossGroup;
    private final EventLoopGroup workerGroup;
    private final GT06ChannelInitializer channelInitializer;
    
    public void start(int port) throws InterruptedException {
        ServerBootstrap bootstrap = new ServerBootstrap();
        bootstrap.group(bossGroup, workerGroup)
                .channel(NioServerSocketChannel.class)
                .childHandler(channelInitializer)
                .option(ChannelOption.SO_BACKLOG, 1024)
                .childOption(ChannelOption.SO_KEEPALIVE, true);
        
        ChannelFuture future = bootstrap.bind(port).sync();
        future.channel().closeFuture().sync();
    }
}
```

#### 3.1.2 协议解析模块 (gt06-protocol)
```java
// 协议解码器
public class GT06Decoder extends ByteToMessageDecoder {
    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) {
        if (in.readableBytes() < 10) return; // 最小包长度
        
        // 检查起始位
        if (in.getShort(in.readerIndex()) != 0x7878) {
            in.skipBytes(1);
            return;
        }
        
        // 解析包长度
        int packetLength = in.getUnsignedByte(in.readerIndex() + 2) + 5;
        if (in.readableBytes() < packetLength) return;
        
        // 提取完整数据包
        ByteBuf packet = in.readBytes(packetLength);
        GT06Message message = parseMessage(packet);
        out.add(message);
    }
}
```

#### 3.1.3 业务处理模块 (gt06-service)
```java
// 消息处理服务
@Service
public class GT06MessageService {
    
    @Autowired
    private DeviceService deviceService;
    
    @Autowired
    private LocationService locationService;
    
    public GT06Response handleMessage(GT06Message message) {
        return switch (message.getProtocolNumber()) {
            case 0x01 -> handleLoginMessage((LoginMessage) message);
            case 0x12 -> handleLocationMessage((LocationMessage) message);
            case 0x13 -> handleHeartbeatMessage((HeartbeatMessage) message);
            case 0x16 -> handleAlarmMessage((AlarmMessage) message);
            default -> handleUnknownMessage(message);
        };
    }
}
```

### 3.2 数据模型设计

#### 3.2.1 设备信息表 (t_device)
```sql
CREATE TABLE t_device (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    imei VARCHAR(15) UNIQUE NOT NULL COMMENT '设备IMEI号',
    imsi VARCHAR(15) COMMENT 'SIM卡IMSI号',
    iccid VARCHAR(20) COMMENT 'SIM卡ICCID号',
    device_name VARCHAR(100) COMMENT '设备名称',
    device_type VARCHAR(50) COMMENT '设备类型',
    status TINYINT DEFAULT 1 COMMENT '设备状态 1-在线 0-离线',
    last_heartbeat DATETIME COMMENT '最后心跳时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3.2.2 位置信息表 (t_location)
```sql
CREATE TABLE t_location (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_id BIGINT NOT NULL,
    imei VARCHAR(15) NOT NULL,
    latitude DECIMAL(10,7) COMMENT '纬度',
    longitude DECIMAL(10,7) COMMENT '经度',
    altitude INT COMMENT '海拔高度',
    speed SMALLINT COMMENT '速度 km/h',
    direction SMALLINT COMMENT '方向角度',
    satellite_count TINYINT COMMENT '卫星数量',
    gps_time DATETIME COMMENT 'GPS时间',
    mcc SMALLINT COMMENT '移动国家代码',
    mnc TINYINT COMMENT '移动网络代码',
    lac INT COMMENT '位置区码',
    cell_id INT COMMENT '基站ID',
    signal_strength TINYINT COMMENT '信号强度',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_device_time (device_id, gps_time),
    INDEX idx_imei_time (imei, gps_time)
);
```

#### 3.2.3 报警信息表 (t_alarm)
```sql
CREATE TABLE t_alarm (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_id BIGINT NOT NULL,
    imei VARCHAR(15) NOT NULL,
    alarm_type TINYINT NOT NULL COMMENT '报警类型',
    alarm_desc VARCHAR(100) COMMENT '报警描述',
    latitude DECIMAL(10,7) COMMENT '报警位置纬度',
    longitude DECIMAL(10,7) COMMENT '报警位置经度',
    alarm_time DATETIME COMMENT '报警时间',
    status TINYINT DEFAULT 0 COMMENT '处理状态 0-未处理 1-已处理',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_device_alarm (device_id, alarm_time),
    INDEX idx_status (status)
);
```

### 3.3 核心类设计

#### 3.3.1 消息基类
```java
public abstract class GT06Message {
    protected byte[] startBits = {0x78, 0x78};
    protected int length;
    protected int protocolNumber;
    protected byte[] content;
    protected int serialNumber;
    protected int crc;
    protected byte[] stopBits = {0x0D, 0x0A};
    
    // 抽象方法
    public abstract byte[] encode();
    public abstract void decode(ByteBuf buffer);
    public abstract boolean validate();
}
```

#### 3.3.2 登录消息
```java
@Data
public class LoginMessage extends GT06Message {
    private String imei; // 15位IMEI号
    
    @Override
    public void decode(ByteBuf buffer) {
        // 跳过起始位和长度
        buffer.skipBytes(3);
        
        // 读取IMEI (8字节BCD编码)
        byte[] imeiBytes = new byte[8];
        buffer.readBytes(imeiBytes);
        this.imei = BCDUtil.decode(imeiBytes);
        
        // 读取序列号和校验码
        this.serialNumber = buffer.readUnsignedShort();
        this.crc = buffer.readUnsignedShort();
    }
}
```

#### 3.3.3 位置消息
```java
@Data
public class LocationMessage extends GT06Message {
    private LocalDateTime dateTime;
    private int satelliteCount;
    private double latitude;
    private double longitude;
    private int speed;
    private int direction;
    private boolean gpsFixed;
    private boolean accOn;
    private int mcc;
    private int mnc;
    private int lac;
    private int cellId;
    private Long mileage; // 里程，可选字段
    
    @Override
    public void decode(ByteBuf buffer) {
        // 解析日期时间 (6字节)
        int year = 2000 + buffer.readUnsignedByte();
        int month = buffer.readUnsignedByte();
        int day = buffer.readUnsignedByte();
        int hour = buffer.readUnsignedByte();
        int minute = buffer.readUnsignedByte();
        int second = buffer.readUnsignedByte();
        this.dateTime = LocalDateTime.of(year, month, day, hour, minute, second);
        
        // 解析GPS信息
        int gpsInfo = buffer.readUnsignedByte();
        this.satelliteCount = gpsInfo & 0x0F;
        
        // 解析经纬度 (各4字节)
        this.latitude = buffer.readUnsignedInt() / 30000.0 / 60.0;
        this.longitude = buffer.readUnsignedInt() / 30000.0 / 60.0;
        
        // 解析速度
        this.speed = buffer.readUnsignedByte();
        
        // 解析方向和状态
        int directionAndStatus = buffer.readUnsignedShort();
        this.direction = directionAndStatus & 0x03FF;
        this.gpsFixed = (directionAndStatus & 0x1000) != 0;
        this.accOn = (directionAndStatus & 0x0200) != 0;
        
        // 解析LBS信息
        this.mcc = buffer.readUnsignedShort();
        this.mnc = buffer.readUnsignedByte();
        this.lac = buffer.readUnsignedShort();
        this.cellId = buffer.readUnsignedMedium();
        
        // 检查是否有里程信息
        if (buffer.readableBytes() > 4) { // 序列号(2) + CRC(2) = 4
            this.mileage = buffer.readUnsignedInt();
        }
    }
}
```

### 3.4 CRC校验实现
```java
public class CRCUtil {
    private static final int[] CRC_TABLE = {
        0x0000, 0x1189, 0x2312, 0x329B, 0x4624, 0x57AD, 0x6536, 0x74BF,
        // ... 完整的CRC-ITU查表
    };
    
    public static int calculateCRC(byte[] data, int offset, int length) {
        int crc = 0xFFFF;
        for (int i = offset; i < offset + length; i++) {
            crc = (crc >> 8) ^ CRC_TABLE[(crc ^ (data[i] & 0xFF)) & 0xFF];
        }
        return (~crc) & 0xFFFF;
    }
    
    public static boolean verifyCRC(byte[] packet) {
        if (packet.length < 7) return false;
        
        int dataLength = packet.length - 4; // 除去CRC(2)和停止位(2)
        int calculatedCRC = calculateCRC(packet, 2, dataLength - 2);
        int receivedCRC = ((packet[dataLength] & 0xFF) << 8) | (packet[dataLength + 1] & 0xFF);
        
        return calculatedCRC == receivedCRC;
    }
}
```

## 4. 业务流程设计

### 4.1 设备连接流程
```mermaid
sequenceDiagram
    participant Device as GPS设备
    participant Server as GT06服务器
    participant DB as 数据库
    
    Device->>Server: TCP连接建立
    Device->>Server: 登录包(0x01)
    Server->>DB: 验证IMEI
    Server->>Device: 登录响应
    Device->>Server: 心跳包(0x13)
    Server->>Device: 心跳响应
    loop 定位数据上报
        Device->>Server: 位置包(0x12)
        Server->>DB: 存储位置信息
    end
```

### 4.2 报警处理流程
```mermaid
flowchart TD
    A[接收报警包] --> B[解析报警类型]
    B --> C{是否紧急报警}
    C -->|是| D[立即推送通知]
    C -->|否| E[记录报警信息]
    D --> F[存储报警记录]
    E --> F
    F --> G[触发业务规则]
    G --> H[发送响应包]
```

### 4.3 指令下发流程
```mermaid
sequenceDiagram
    participant API as 外部API
    participant Server as GT06服务器
    participant Device as GPS设备
    
    API->>Server: 下发指令请求
    Server->>Device: 指令包(0x80)
    Device->>Server: 指令响应(0x15)
    Server->>API: 返回执行结果
```

## 5. 配置管理

### 5.1 应用配置 (application.yml)
```yaml
server:
  port: 8080

gt06:
  server:
    port: 8888
    boss-threads: 1
    worker-threads: 4
    so-backlog: 1024
    keep-alive: true
  
  protocol:
    max-frame-length: 1024
    heartbeat-timeout: 300 # 5分钟
    login-timeout: 30 # 30秒

spring:
  datasource:
    url: ***************************************************************************************
    username: gt06_user
    password: gt06_pass
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
    
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest

logging:
  level:
    com.gt06: DEBUG
    io.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

## 6. 部署方案

### 6.1 Docker部署
```dockerfile
FROM openjdk:21-jdk-slim

WORKDIR /app

COPY target/gt06-server-1.0.0.jar app.jar

EXPOSE 8080 8888

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 6.2 Docker Compose
```yaml
version: '3.8'

services:
  gt06-server:
    build: .
    ports:
      - "8080:8080"
      - "8888:8888"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
    depends_on:
      - mysql
      - redis
      
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: gt06_db
      MYSQL_USER: gt06_user
      MYSQL_PASSWORD: gt06_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

## 7. 监控与运维

### 7.1 健康检查
```java
@RestController
@RequestMapping("/actuator")
public class HealthController {
    
    @Autowired
    private GT06Server gt06Server;
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "UP");
        status.put("gt06Server", gt06Server.isRunning() ? "UP" : "DOWN");
        status.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(status);
    }
}
```

### 7.2 性能监控
- 使用Micrometer集成Prometheus监控
- 监控连接数、消息处理速度、错误率
- 设置告警规则

## 8. 安全考虑

### 8.1 网络安全
- 使用SSL/TLS加密通信（可选）
- IP白名单限制
- 连接频率限制

### 8.2 数据安全
- 敏感数据加密存储
- 定期数据备份
- 访问权限控制

## 9. 扩展性设计

### 9.1 水平扩展
- 支持多实例部署
- 使用Redis共享会话状态
- 负载均衡配置

### 9.2 协议扩展
- 插件化协议处理器
- 支持自定义协议号
- 版本兼容性管理

## 10. 测试策略

### 10.1 单元测试
- 协议解析测试
- CRC校验测试
- 业务逻辑测试

### 10.2 集成测试
- 端到端通信测试
- 数据库集成测试
- 性能压力测试

### 10.3 模拟测试
- 设备模拟器开发
- 异常场景测试
- 并发连接测试

---

## 附录

### A. 协议号对照表
| 协议号 | 十六进制 | 说明 | 实现优先级 |
|--------|----------|------|------------|
| 登录包 | 0x01 | 设备登录认证 | 高 |
| 定位数据包 | 0x12 | GPS/LBS位置信息 | 高 |
| 心跳包 | 0x13 | 保持连接 | 高 |
| 字符串信息 | 0x15 | 指令响应 | 中 |
| 报警信息 | 0x16 | 报警数据 | 高 |
| 查询地址 | 0x1A | GPS地址查询 | 低 |
| LBS多基站 | 0x18 | 多基站信息 | 中 |
| LBS+WIFI | 0x2C | 基站+WIFI定位 | 中 |
| 下发指令 | 0x80 | 服务器指令 | 高 |
| IMSI上报 | 0x90 | SIM卡信息 | 中 |
| ICCID上报 | 0x94 | SIM卡标识 | 中 |
| 录音协议 | 0x8D | 录音文件传输 | 低 |

### B. 报警类型对照表
| 代码 | 十六进制 | 报警类型 | 处理级别 |
|------|----------|----------|----------|
| 0 | 0x00 | 正常 | 信息 |
| 1 | 0x01 | SOS报警 | 紧急 |
| 2 | 0x02 | 断电报警 | 重要 |
| 3 | 0x03 | 震动报警 | 一般 |
| 4 | 0x04 | 进围栏报警 | 一般 |
| 5 | 0x05 | 出围栏报警 | 一般 |
| 6 | 0x06 | 超速报警 | 重要 |
| 19 | 0x13 | 防拆报警 | 重要 |
| 38 | 0x26 | 急加速报警 | 一般 |
| 39 | 0x27 | 急减速报警 | 一般 |
| 40 | 0x28 | 急转弯报警 | 一般 |
| 41 | 0x29 | 碰撞报警 | 紧急 |

### C. 开发环境搭建
1. 安装JDK 21
2. 安装Maven 3.9+
3. 安装MySQL 8.0
4. 安装Redis 7.x
5. 配置IDE (IntelliJ IDEA推荐)
6. 导入项目依赖

### D. 项目结构
```
gt06-protocol-system/
├── gt06-common/          # 公共模块
├── gt06-protocol/        # 协议解析模块
├── gt06-network/         # 网络通信模块
├── gt06-service/         # 业务服务模块
├── gt06-web/            # Web接口模块
├── gt06-admin/          # 管理后台模块
└── gt06-test/           # 测试模块
```

本设计文档为GT06协议系统的完整技术方案，涵盖了从协议解析到业务处理的各个层面，为后续开发提供了详细的技术指导。