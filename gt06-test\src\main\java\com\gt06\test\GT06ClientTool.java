package com.gt06.test;

import com.gt06.test.client.GT06TestClient;
import lombok.extern.slf4j.Slf4j;

import java.util.Scanner;
import java.util.concurrent.CompletableFuture;

/**
 * GT06客户端测试工具
 * 命令行交互式测试工具
 * 
 * <AUTHOR> Test
 * @version 1.0.0
 */
@Slf4j
public class GT06ClientTool {
    
    private static final String DEFAULT_HOST = "localhost";
    private static final int DEFAULT_PORT = 8888;
    
    private GT06TestClient client;
    private Scanner scanner;
    private boolean running = true;
    
    public static void main(String[] args) {
        new GT06ClientTool().run(args);
    }
    
    public void run(String[] args) {
        scanner = new Scanner(System.in);
        
        printWelcome();
        
        // 解析命令行参数
        String host = DEFAULT_HOST;
        int port = DEFAULT_PORT;
        String deviceId = "TOOL_TEST_001";
        
        if (args.length >= 1) host = args[0];
        if (args.length >= 2) port = Integer.parseInt(args[1]);
        if (args.length >= 3) deviceId = args[2];
        
        client = new GT06TestClient(host, port, deviceId);
        
        log.info("🔧 GT06 Client Tool initialized");
        log.info("Target server: {}:{}", host, port);
        log.info("Device ID: {}", deviceId);
        
        // 主循环
        while (running) {
            printMenu();
            String choice = scanner.nextLine().trim();
            handleCommand(choice);
        }
        
        cleanup();
    }
    
    private void printWelcome() {
        System.out.println("╔══════════════════════════════════════╗");
        System.out.println("║        GT06 Client Test Tool         ║");
        System.out.println("║     Interactive Testing Interface   ║");
        System.out.println("╚══════════════════════════════════════╝");
        System.out.println();
    }
    
    private void printMenu() {
        System.out.println("\n📋 Available Commands:");
        System.out.println("  1. connect    - Connect to GT06 server");
        System.out.println("  2. login      - Send login message");
        System.out.println("  3. location   - Send location message");
        System.out.println("  4. heartbeat  - Send heartbeat message");
        System.out.println("  5. auto       - Start automatic heartbeat");
        System.out.println("  6. status     - Show connection status");
        System.out.println("  7. test       - Run quick test sequence");
        System.out.println("  8. stress     - Run stress test");
        System.out.println("  9. disconnect - Disconnect from server");
        System.out.println("  0. exit       - Exit the tool");
        System.out.println();
        System.out.print("Enter your choice: ");
    }
    
    private void handleCommand(String choice) {
        try {
            switch (choice.toLowerCase()) {
                case "1":
                case "connect":
                    handleConnect();
                    break;
                case "2":
                case "login":
                    handleLogin();
                    break;
                case "3":
                case "location":
                    handleLocation();
                    break;
                case "4":
                case "heartbeat":
                    handleHeartbeat();
                    break;
                case "5":
                case "auto":
                    handleAutoHeartbeat();
                    break;
                case "6":
                case "status":
                    handleStatus();
                    break;
                case "7":
                case "test":
                    handleQuickTest();
                    break;
                case "8":
                case "stress":
                    handleStressTest();
                    break;
                case "9":
                case "disconnect":
                    handleDisconnect();
                    break;
                case "0":
                case "exit":
                    running = false;
                    break;
                default:
                    System.out.println("❌ Invalid choice. Please try again.");
            }
        } catch (Exception e) {
            log.error("Command execution failed: {}", e.getMessage());
            System.out.println("❌ Command failed: " + e.getMessage());
        }
    }
    
    private void handleConnect() {
        System.out.println("🔌 Connecting to GT06 server...");
        boolean success = client.connect();
        if (success) {
            System.out.println("✅ Connected successfully!");
        } else {
            System.out.println("❌ Connection failed!");
        }
    }
    
    private void handleLogin() {
        if (!client.isConnected()) {
            System.out.println("❌ Not connected. Please connect first.");
            return;
        }
        
        System.out.println("📤 Sending login message...");
        boolean success = client.sendLogin();
        if (success) {
            System.out.println("✅ Login message sent successfully!");
        } else {
            System.out.println("❌ Login failed!");
        }
    }
    
    private void handleLocation() {
        if (!client.isConnected()) {
            System.out.println("❌ Not connected. Please connect first.");
            return;
        }
        
        System.out.print("Enter latitude (default 39.9042): ");
        String latStr = scanner.nextLine().trim();
        double latitude = latStr.isEmpty() ? 39.9042 : Double.parseDouble(latStr);
        
        System.out.print("Enter longitude (default 116.4074): ");
        String lngStr = scanner.nextLine().trim();
        double longitude = lngStr.isEmpty() ? 116.4074 : Double.parseDouble(lngStr);
        
        System.out.print("Enter speed (default 60): ");
        String speedStr = scanner.nextLine().trim();
        int speed = speedStr.isEmpty() ? 60 : Integer.parseInt(speedStr);
        
        System.out.println("📍 Sending location message...");
        boolean success = client.sendLocation(latitude, longitude, speed);
        if (success) {
            System.out.println("✅ Location message sent successfully!");
        } else {
            System.out.println("❌ Location message failed!");
        }
    }
    
    private void handleHeartbeat() {
        if (!client.isConnected()) {
            System.out.println("❌ Not connected. Please connect first.");
            return;
        }
        
        System.out.println("💓 Sending heartbeat message...");
        boolean success = client.sendHeartbeat();
        if (success) {
            System.out.println("✅ Heartbeat sent successfully!");
        } else {
            System.out.println("❌ Heartbeat failed!");
        }
    }
    
    private void handleAutoHeartbeat() {
        if (!client.isConnected()) {
            System.out.println("❌ Not connected. Please connect first.");
            return;
        }
        
        System.out.print("Enter heartbeat interval in seconds (default 30): ");
        String intervalStr = scanner.nextLine().trim();
        int interval = intervalStr.isEmpty() ? 30 : Integer.parseInt(intervalStr);
        
        System.out.println("🔄 Starting automatic heartbeat every " + interval + " seconds...");
        client.startHeartbeat(interval);
        System.out.println("✅ Automatic heartbeat started!");
    }
    
    private void handleStatus() {
        System.out.println("📊 Connection Status:");
        System.out.println("  - Connected: " + (client.isConnected() ? "✅ Yes" : "❌ No"));
        System.out.println("  - Device ID: " + client.getDeviceId());
    }
    
    private void handleQuickTest() {
        System.out.println("🧪 Running quick test sequence...");
        
        CompletableFuture.runAsync(() -> {
            try {
                // 连接
                System.out.println("1/4 Connecting...");
                if (!client.connect()) {
                    System.out.println("❌ Quick test failed at connection");
                    return;
                }
                
                // 登录
                System.out.println("2/4 Logging in...");
                if (!client.sendLogin()) {
                    System.out.println("❌ Quick test failed at login");
                    return;
                }
                
                Thread.sleep(1000);
                
                // 发送位置
                System.out.println("3/4 Sending location...");
                if (!client.sendLocation(39.9042, 116.4074, 60)) {
                    System.out.println("❌ Quick test failed at location");
                    return;
                }
                
                Thread.sleep(1000);
                
                // 发送心跳
                System.out.println("4/4 Sending heartbeat...");
                if (!client.sendHeartbeat()) {
                    System.out.println("❌ Quick test failed at heartbeat");
                    return;
                }
                
                System.out.println("✅ Quick test completed successfully!");
                
            } catch (Exception e) {
                System.out.println("❌ Quick test failed: " + e.getMessage());
            }
        });
    }
    
    private void handleStressTest() {
        System.out.print("Enter number of messages to send (default 100): ");
        String countStr = scanner.nextLine().trim();
        int messageCount = countStr.isEmpty() ? 100 : Integer.parseInt(countStr);
        
        System.out.println("🚀 Running stress test with " + messageCount + " messages...");
        
        CompletableFuture.runAsync(() -> {
            try {
                if (!client.isConnected()) {
                    if (!client.connect() || !client.sendLogin()) {
                        System.out.println("❌ Stress test failed to establish connection");
                        return;
                    }
                }
                
                long startTime = System.currentTimeMillis();
                int successCount = 0;
                
                for (int i = 0; i < messageCount; i++) {
                    boolean success;
                    if (i % 5 == 0) {
                        success = client.sendHeartbeat();
                    } else {
                        success = client.sendLocation(
                            39.9042 + Math.random() * 0.01,
                            116.4074 + Math.random() * 0.01,
                            (int)(Math.random() * 100)
                        );
                    }
                    
                    if (success) successCount++;
                    
                    if ((i + 1) % 20 == 0) {
                        System.out.println("Progress: " + (i + 1) + "/" + messageCount);
                    }
                }
                
                long duration = System.currentTimeMillis() - startTime;
                double rate = (double) messageCount / duration * 1000;
                
                System.out.println("📊 Stress test results:");
                System.out.println("  - Messages sent: " + messageCount);
                System.out.println("  - Successful: " + successCount);
                System.out.println("  - Success rate: " + (successCount * 100.0 / messageCount) + "%");
                System.out.println("  - Duration: " + duration + "ms");
                System.out.println("  - Rate: " + String.format("%.2f", rate) + " msg/sec");
                
            } catch (Exception e) {
                System.out.println("❌ Stress test failed: " + e.getMessage());
            }
        });
    }
    
    private void handleDisconnect() {
        System.out.println("🔌 Disconnecting...");
        client.disconnect();
        System.out.println("✅ Disconnected!");
    }
    
    private void cleanup() {
        System.out.println("\n👋 Goodbye!");
        if (client != null) {
            client.disconnect();
        }
        if (scanner != null) {
            scanner.close();
        }
    }
}
