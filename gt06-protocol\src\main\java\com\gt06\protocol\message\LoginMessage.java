package com.gt06.protocol.message;

import com.gt06.common.constants.GT06Constants;
import com.gt06.common.util.BCDUtil;
import com.gt06.common.util.ByteUtil;
import io.netty.buffer.ByteBuf;
import lombok.Getter;
import lombok.Setter;

/**
 * GT06协议登录消息 (0x01)
 * 设备向服务器发送的登录认证消息
 * 
 * 消息格式：
 * 起始位(2) + 包长度(1) + 协议号(1) + IMEI(8) + 序列号(2) + CRC(2) + 停止位(2)
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Getter
@Setter
public class LoginMessage extends GT06Message {
    
    /** 设备IMEI号（15位数字） */
    private String imei;
    
    /** 设备型号信息（可选，某些版本包含） */
    private String deviceModel;
    
    /** 时区信息（可选） */
    private Integer timeZone;
    
    /**
     * 构造函数
     */
    public LoginMessage() {
        super(GT06Constants.PROTOCOL_LOGIN);
    }
    
    /**
     * 构造函数
     * 
     * @param imei 设备IMEI号
     */
    public LoginMessage(String imei) {
        super(GT06Constants.PROTOCOL_LOGIN);
        this.imei = imei;
    }
    
    @Override
    public byte[] encode() {
        if (imei == null || imei.length() != 15) {
            throw new IllegalArgumentException("IMEI must be 15 digits");
        }
        
        // 编码IMEI为8字节BCD格式
        byte[] imeiBytes = BCDUtil.encodeIMEI(imei);
        
        // 创建包头
        byte[] header = createPacketHeader(imeiBytes.length);
        
        // 组装完整数据包（不包含CRC和停止位）
        byte[] dataForCRC = ByteUtil.concat(header, imeiBytes);
        
        // 创建包尾
        byte[] tail = createPacketTail(dataForCRC);
        
        // 返回完整数据包
        return ByteUtil.concat(dataForCRC, tail);
    }
    
    @Override
    public void decode(ByteBuf buffer) {
        // 读取包头
        readPacketHeader(buffer);
        
        // 计算内容长度
        int contentLength = length - 1 - 2 - 2; // 总长度 - 协议号 - 序列号 - CRC
        
        if (contentLength < 8) {
            throw new IllegalArgumentException("Login message content too short");
        }
        
        // 读取IMEI（8字节BCD编码）
        byte[] imeiBytes = new byte[8];
        buffer.readBytes(imeiBytes);
        this.imei = BCDUtil.decodeIMEI(imeiBytes);
        
        // 设置设备IMEI到消息基类
        this.deviceImei = this.imei;
        
        // 如果还有额外内容，可能包含设备型号或时区信息
        int remainingContent = contentLength - 8;
        if (remainingContent > 0) {
            byte[] extraContent = new byte[remainingContent];
            buffer.readBytes(extraContent);
            parseExtraContent(extraContent);
        }
        
        // 读取包尾
        readPacketTail(buffer);
    }
    
    /**
     * 解析额外的内容（设备型号、时区等）
     * 
     * @param extraContent 额外内容字节数组
     */
    private void parseExtraContent(byte[] extraContent) {
        // 根据协议版本和设备类型，额外内容可能包含：
        // - 设备型号信息
        // - 时区信息
        // - 软件版本号等
        
        if (extraContent.length >= 2) {
            // 假设前2字节为时区信息（有符号整数）
            this.timeZone = ByteUtil.bytes2ToInt(extraContent, 0);
            if (this.timeZone > 12 || this.timeZone < -12) {
                this.timeZone = null; // 无效时区，忽略
            }
        }
        
        if (extraContent.length > 2) {
            // 剩余内容可能是设备型号（ASCII字符串）
            try {
                this.deviceModel = new String(extraContent, 2, extraContent.length - 2, "ASCII").trim();
                // 移除非打印字符
                this.deviceModel = this.deviceModel.replaceAll("[\\x00-\\x1F\\x7F]", "");
                if (this.deviceModel.isEmpty()) {
                    this.deviceModel = null;
                }
            } catch (Exception e) {
                // 解析失败，忽略设备型号
                this.deviceModel = null;
            }
        }
    }
    
    @Override
    public boolean validate() {
        // 验证协议号
        if (protocolNumber != GT06Constants.PROTOCOL_LOGIN) {
            return false;
        }
        
        // 验证IMEI
        if (imei == null || imei.length() != 15 || !imei.matches("\\d{15}")) {
            return false;
        }
        
        // 验证序列号
        if (serialNumber < 0 || serialNumber > 0xFFFF) {
            return false;
        }
        
        // 验证时区（如果存在）
        if (timeZone != null && (timeZone < -12 || timeZone > 12)) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public String getMessageDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("Login from device ").append(imei);
        
        if (deviceModel != null) {
            desc.append(", model: ").append(deviceModel);
        }
        
        if (timeZone != null) {
            desc.append(", timezone: ").append(timeZone > 0 ? "+" : "").append(timeZone);
        }
        
        return desc.toString();
    }
    
    @Override
    public GT06Message createResponse() {
        return ResponseMessage.createLoginResponse(this.serialNumber);
    }
    
    /**
     * 检查IMEI的校验位是否正确（Luhn算法）
     * 
     * @return IMEI校验是否通过
     */
    public boolean isIMEIValid() {
        if (imei == null || imei.length() != 15) {
            return false;
        }
        
        // 使用Luhn算法验证IMEI
        int sum = 0;
        boolean alternate = false;
        
        // 从右到左处理前14位数字
        for (int i = 13; i >= 0; i--) {
            int digit = Character.getNumericValue(imei.charAt(i));
            
            if (alternate) {
                digit *= 2;
                if (digit > 9) {
                    digit = (digit % 10) + 1;
                }
            }
            
            sum += digit;
            alternate = !alternate;
        }
        
        // 计算校验位
        int checkDigit = (10 - (sum % 10)) % 10;
        int lastDigit = Character.getNumericValue(imei.charAt(14));
        
        return checkDigit == lastDigit;
    }
    
    /**
     * 获取设备厂商信息（基于IMEI的TAC部分）
     * 
     * @return 设备厂商信息
     */
    public String getDeviceManufacturer() {
        if (imei == null || imei.length() < 8) {
            return "Unknown";
        }
        
        // TAC (Type Allocation Code) 是IMEI的前8位
        String tac = imei.substring(0, 8);
        
        // 这里可以根据TAC数据库返回具体的厂商信息
        // 简化实现，返回TAC信息
        return "TAC: " + tac;
    }
}
