package com.gt06.network.codec;

import com.gt06.common.util.ByteUtil;
import com.gt06.protocol.message.GT06Message;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import lombok.extern.slf4j.Slf4j;

/**
 * GT06协议消息编码器
 * 将GT06消息对象编码为字节流
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
public class GT06MessageEncoder extends MessageToByteEncoder<GT06Message> {
    
    /** 编码统计 */
    private long totalMessages = 0;
    private long successfulMessages = 0;
    private long failedMessages = 0;
    
    @Override
    protected void encode(ChannelHandlerContext ctx, GT06Message msg, ByteBuf out) throws Exception {
        try {
            // 编码消息为字节数组
            byte[] messageBytes = msg.encode();
            
            if (messageBytes == null || messageBytes.length == 0) {
                log.warn("Empty message bytes for message: {}", msg.getMessageDescription());
                failedMessages++;
                return;
            }
            
            // 写入到输出缓冲区
            out.writeBytes(messageBytes);
            
            totalMessages++;
            successfulMessages++;
            
            if (log.isDebugEnabled()) {
                log.debug("Encoded message: {} to {} ({} bytes)", 
                    msg.getMessageDescription(), 
                    ctx.channel().remoteAddress(),
                    messageBytes.length);
                
                if (log.isTraceEnabled()) {
                    log.trace("Encoded bytes: {}", ByteUtil.bytesToHexWithSpace(messageBytes));
                }
            }
            
        } catch (Exception e) {
            log.error("Failed to encode GT06 message: {} for channel {}", 
                msg.getMessageDescription(), ctx.channel().remoteAddress(), e);
            
            failedMessages++;
            
            // 重新抛出异常，让上层处理
            throw e;
        }
    }
    

    
    /**
     * 获取编码统计信息
     * 
     * @return 编码统计
     */
    public EncoderStats getStats() {
        return new EncoderStats(totalMessages, successfulMessages, failedMessages);
    }
    
    /**
     * 重置统计信息
     */
    public void resetStats() {
        totalMessages = 0;
        successfulMessages = 0;
        failedMessages = 0;
    }
    
    /**
     * 编码器统计信息
     */
    public static class EncoderStats {
        private final long totalMessages;
        private final long successfulMessages;
        private final long failedMessages;
        
        public EncoderStats(long totalMessages, long successfulMessages, long failedMessages) {
            this.totalMessages = totalMessages;
            this.successfulMessages = successfulMessages;
            this.failedMessages = failedMessages;
        }
        
        public long getTotalMessages() { return totalMessages; }
        public long getSuccessfulMessages() { return successfulMessages; }
        public long getFailedMessages() { return failedMessages; }
        
        public double getSuccessRate() {
            return totalMessages > 0 ? (double) successfulMessages / totalMessages : 0.0;
        }
        
        public double getFailureRate() {
            return totalMessages > 0 ? (double) failedMessages / totalMessages : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format("EncoderStats{total=%d, success=%d, failed=%d, successRate=%.2f%%}", 
                totalMessages, successfulMessages, failedMessages, getSuccessRate() * 100);
        }
    }
}
