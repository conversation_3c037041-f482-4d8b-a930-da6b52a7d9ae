package com.gt06.network.session;

import io.netty.channel.Channel;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 设备会话
 * 表示一个设备的连接会话信息
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Getter
@Setter
public class DeviceSession {
    
    /** 网络通道 */
    private final Channel channel;
    
    /** 客户端IP地址 */
    private final String clientIp;
    
    /** 客户端端口 */
    private final int clientPort;
    
    /** 会话创建时间 */
    private final LocalDateTime createTime;
    
    /** 最后活动时间 */
    private volatile LocalDateTime lastActivity;
    
    /** 设备IMEI */
    private volatile String imei;
    
    /** 是否已认证 */
    private volatile boolean authenticated = false;
    
    /** 认证时间 */
    private volatile LocalDateTime authTime;
    
    /** 设备类型 */
    private volatile String deviceType;
    
    /** 设备版本 */
    private volatile String deviceVersion;
    
    /** 最后一次心跳时间 */
    private volatile LocalDateTime lastHeartbeat;
    
    /** 最后一次位置上报时间 */
    private volatile LocalDateTime lastLocationReport;
    
    /** 接收消息计数 */
    private final AtomicLong receivedMessageCount = new AtomicLong(0);
    
    /** 发送消息计数 */
    private final AtomicLong sentMessageCount = new AtomicLong(0);
    
    /** 当前序列号 */
    private final AtomicInteger currentSequence = new AtomicInteger(1);
    
    /** 设备状态 */
    private volatile DeviceStatus deviceStatus = DeviceStatus.UNKNOWN;
    
    /** GPS状态 */
    private volatile boolean gpsFixed = false;
    
    /** ACC状态 */
    private volatile boolean accOn = false;
    
    /** 信号强度 */
    private volatile int signalStrength = 0;
    
    /** 电池电压 */
    private volatile double batteryVoltage = 0.0;
    
    /** 最后位置信息 */
    private volatile LocationInfo lastLocation;
    
    /**
     * 设备状态枚举
     */
    public enum DeviceStatus {
        UNKNOWN("未知"),
        ONLINE("在线"),
        OFFLINE("离线"),
        SLEEPING("休眠"),
        ALARM("报警");
        
        private final String description;
        
        DeviceStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() { return description; }
    }
    
    /**
     * 位置信息
     */
    @Getter
    @Setter
    public static class LocationInfo {
        private double latitude;
        private double longitude;
        private int speed;
        private int direction;
        private LocalDateTime timestamp;
        private boolean gpsFixed;
        
        public LocationInfo(double latitude, double longitude, int speed, int direction, boolean gpsFixed) {
            this.latitude = latitude;
            this.longitude = longitude;
            this.speed = speed;
            this.direction = direction;
            this.gpsFixed = gpsFixed;
            this.timestamp = LocalDateTime.now();
        }
        
        @Override
        public String toString() {
            return String.format("Location{lat=%.6f, lng=%.6f, speed=%d, dir=%d, gps=%s}", 
                latitude, longitude, speed, direction, gpsFixed);
        }
    }
    
    /**
     * 构造函数
     * 
     * @param channel 网络通道
     * @param clientIp 客户端IP
     * @param clientPort 客户端端口
     */
    public DeviceSession(Channel channel, String clientIp, int clientPort) {
        this.channel = channel;
        this.clientIp = clientIp;
        this.clientPort = clientPort;
        this.createTime = LocalDateTime.now();
        this.lastActivity = LocalDateTime.now();
    }
    
    /**
     * 认证会话
     * 
     * @param imei 设备IMEI
     */
    public void authenticate(String imei) {
        this.imei = imei;
        this.authenticated = true;
        this.authTime = LocalDateTime.now();
        this.deviceStatus = DeviceStatus.ONLINE;
        updateLastActivity();
    }
    
    /**
     * 更新最后活动时间
     */
    public void updateLastActivity() {
        this.lastActivity = LocalDateTime.now();
    }
    
    /**
     * 更新心跳时间
     */
    public void updateHeartbeat() {
        this.lastHeartbeat = LocalDateTime.now();
        this.deviceStatus = DeviceStatus.ONLINE;
        updateLastActivity();
    }
    
    /**
     * 更新位置信息
     * 
     * @param latitude 纬度
     * @param longitude 经度
     * @param speed 速度
     * @param direction 方向
     * @param gpsFixed GPS是否定位
     */
    public void updateLocation(double latitude, double longitude, int speed, int direction, boolean gpsFixed) {
        this.lastLocation = new LocationInfo(latitude, longitude, speed, direction, gpsFixed);
        this.lastLocationReport = LocalDateTime.now();
        this.gpsFixed = gpsFixed;
        updateLastActivity();
    }
    
    /**
     * 增加接收消息计数
     */
    public void incrementReceivedCount() {
        receivedMessageCount.incrementAndGet();
        updateLastActivity();
    }
    
    /**
     * 增加发送消息计数
     */
    public void incrementSentCount() {
        sentMessageCount.incrementAndGet();
    }
    
    /**
     * 获取下一个序列号
     * 
     * @return 序列号
     */
    public int getNextSequence() {
        int seq = currentSequence.getAndIncrement();
        if (seq > 0xFFFF) {
            currentSequence.set(1);
            return 1;
        }
        return seq;
    }
    
    /**
     * 检查会话是否活跃
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 是否活跃
     */
    public boolean isActive(int timeoutMinutes) {
        return lastActivity.isAfter(LocalDateTime.now().minusMinutes(timeoutMinutes));
    }
    
    /**
     * 获取会话持续时间（秒）
     * 
     * @return 持续时间
     */
    public long getSessionDurationSeconds() {
        return java.time.Duration.between(createTime, LocalDateTime.now()).getSeconds();
    }
    
    /**
     * 获取认证后持续时间（秒）
     * 
     * @return 认证后持续时间，如果未认证返回0
     */
    public long getAuthenticatedDurationSeconds() {
        if (!authenticated || authTime == null) {
            return 0;
        }
        return java.time.Duration.between(authTime, LocalDateTime.now()).getSeconds();
    }
    
    /**
     * 获取会话信息摘要
     * 
     * @return 会话摘要
     */
    public String getSessionSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Session{");
        summary.append("channel=").append(channel.id().asShortText());
        summary.append(", client=").append(clientIp).append(":").append(clientPort);
        
        if (authenticated && imei != null) {
            summary.append(", imei=").append(imei);
        }
        
        summary.append(", status=").append(deviceStatus.getDescription());
        summary.append(", duration=").append(getSessionDurationSeconds()).append("s");
        summary.append(", recv=").append(receivedMessageCount.get());
        summary.append(", sent=").append(sentMessageCount.get());
        
        if (lastLocation != null) {
            summary.append(", location=").append(lastLocation);
        }
        
        summary.append("}");
        return summary.toString();
    }
    
    @Override
    public String toString() {
        return getSessionSummary();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        DeviceSession that = (DeviceSession) obj;
        return channel.id().equals(that.channel.id());
    }
    
    @Override
    public int hashCode() {
        return channel.id().hashCode();
    }
}
