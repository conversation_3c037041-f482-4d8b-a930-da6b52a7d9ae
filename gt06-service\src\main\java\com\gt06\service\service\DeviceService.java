package com.gt06.service.service;

import com.gt06.service.entity.Device;
import com.gt06.service.repository.DeviceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 设备管理服务
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceService {
    
    private final DeviceRepository deviceRepository;
    
    /**
     * 根据IMEI查找设备
     * 
     * @param imei 设备IMEI
     * @return 设备信息
     */
    @Cacheable(value = "device", key = "#imei")
    public Optional<Device> findByImei(String imei) {
        return deviceRepository.findByImei(imei);
    }
    
    /**
     * 根据IMEI查找启用的设备
     * 
     * @param imei 设备IMEI
     * @return 设备信息
     */
    @Cacheable(value = "device:enabled", key = "#imei")
    public Optional<Device> findEnabledByImei(String imei) {
        return deviceRepository.findByImeiAndEnabled(imei, true);
    }
    
    /**
     * 保存设备信息
     * 
     * @param device 设备信息
     * @return 保存后的设备信息
     */
    @Transactional
    @CacheEvict(value = {"device", "device:enabled"}, key = "#device.imei")
    public Device save(Device device) {
        if (device.getId() == null) {
            // 新设备注册
            device.setRegisterTime(LocalDateTime.now());
            device.setStatus(Device.DeviceStatus.INACTIVE);
            device.setOnline(false);
            log.info("New device registered: IMEI={}, Name={}", device.getImei(), device.getDeviceName());
        } else {
            log.debug("Device updated: IMEI={}", device.getImei());
        }
        
        return deviceRepository.save(device);
    }
    
    /**
     * 设备认证（首次登录时调用）
     * 
     * @param imei 设备IMEI
     * @param deviceType 设备类型
     * @param softwareVersion 软件版本
     * @return 认证结果
     */
    @Transactional
    public boolean authenticateDevice(String imei, String deviceType, String softwareVersion) {
        Optional<Device> deviceOpt = findEnabledByImei(imei);
        
        if (deviceOpt.isEmpty()) {
            // 自动注册新设备
            Device newDevice = new Device();
            newDevice.setImei(imei);
            newDevice.setDeviceType(deviceType);
            newDevice.setSoftwareVersion(softwareVersion);
            newDevice.setEnabled(true);
            newDevice.setStatus(Device.DeviceStatus.NORMAL);
            
            save(newDevice);
            log.info("Auto-registered new device: IMEI={}", imei);
            return true;
        }
        
        Device device = deviceOpt.get();
        
        // 检查设备是否被禁用
        if (!device.isEnabled()) {
            log.warn("Device authentication failed - device disabled: IMEI={}", imei);
            return false;
        }
        
        // 检查服务是否过期
        if (device.isServiceExpired()) {
            log.warn("Device authentication failed - service expired: IMEI={}", imei);
            return false;
        }
        
        // 更新设备信息
        device.setDeviceType(deviceType);
        device.setSoftwareVersion(softwareVersion);
        device.setOnline();
        
        save(device);
        log.info("Device authenticated successfully: IMEI={}", imei);
        return true;
    }
    
    /**
     * 设备上线
     * 
     * @param imei 设备IMEI
     */
    @Transactional
    @CacheEvict(value = {"device", "device:enabled"}, key = "#imei")
    public void deviceOnline(String imei) {
        Optional<Device> deviceOpt = findByImei(imei);
        if (deviceOpt.isPresent()) {
            Device device = deviceOpt.get();
            device.setOnline();
            deviceRepository.save(device);
            log.info("Device online: IMEI={}", imei);
        }
    }
    
    /**
     * 设备离线
     * 
     * @param imei 设备IMEI
     */
    @Transactional
    @CacheEvict(value = {"device", "device:enabled"}, key = "#imei")
    public void deviceOffline(String imei) {
        Optional<Device> deviceOpt = findByImei(imei);
        if (deviceOpt.isPresent()) {
            Device device = deviceOpt.get();
            device.setOffline();
            deviceRepository.save(device);
            log.info("Device offline: IMEI={}", imei);
        }
    }
    
    /**
     * 批量更新设备在线状态
     * 
     * @param imeis IMEI列表
     * @param online 在线状态
     */
    @Transactional
    @CacheEvict(value = {"device", "device:enabled"}, allEntries = true)
    public void batchUpdateOnlineStatus(List<String> imeis, boolean online) {
        int updated = deviceRepository.batchUpdateOnlineStatus(imeis, online, LocalDateTime.now());
        log.info("Batch updated {} devices online status to: {}", updated, online);
    }
    
    /**
     * 更新设备状态
     * 
     * @param imei 设备IMEI
     * @param status 设备状态
     */
    @Transactional
    @CacheEvict(value = {"device", "device:enabled"}, key = "#imei")
    public void updateDeviceStatus(String imei, Device.DeviceStatus status) {
        Optional<Device> deviceOpt = findByImei(imei);
        if (deviceOpt.isPresent()) {
            Device device = deviceOpt.get();
            device.setStatus(status);
            deviceRepository.save(device);
            log.info("Device status updated: IMEI={}, Status={}", imei, status);
        }
    }
    
    /**
     * 启用设备
     * 
     * @param imei 设备IMEI
     */
    @Transactional
    @CacheEvict(value = {"device", "device:enabled"}, key = "#imei")
    public void enableDevice(String imei) {
        Optional<Device> deviceOpt = findByImei(imei);
        if (deviceOpt.isPresent()) {
            Device device = deviceOpt.get();
            device.setEnabled(true);
            if (device.getStatus() == Device.DeviceStatus.DISABLED) {
                device.setStatus(Device.DeviceStatus.NORMAL);
            }
            deviceRepository.save(device);
            log.info("Device enabled: IMEI={}", imei);
        }
    }
    
    /**
     * 禁用设备
     * 
     * @param imei 设备IMEI
     */
    @Transactional
    @CacheEvict(value = {"device", "device:enabled"}, key = "#imei")
    public void disableDevice(String imei) {
        Optional<Device> deviceOpt = findByImei(imei);
        if (deviceOpt.isPresent()) {
            Device device = deviceOpt.get();
            device.setEnabled(false);
            device.setStatus(Device.DeviceStatus.DISABLED);
            device.setOffline();
            deviceRepository.save(device);
            log.info("Device disabled: IMEI={}", imei);
        }
    }
    
    /**
     * 删除设备
     * 
     * @param imei 设备IMEI
     */
    @Transactional
    @CacheEvict(value = {"device", "device:enabled"}, key = "#imei")
    public void deleteDevice(String imei) {
        Optional<Device> deviceOpt = findByImei(imei);
        if (deviceOpt.isPresent()) {
            deviceRepository.delete(deviceOpt.get());
            log.info("Device deleted: IMEI={}", imei);
        }
    }
    
    /**
     * 分页查询设备
     * 
     * @param pageable 分页参数
     * @return 设备分页列表
     */
    public Page<Device> findAll(Pageable pageable) {
        return deviceRepository.findAll(pageable);
    }
    
    /**
     * 根据条件查询设备
     * 
     * @param status 设备状态
     * @param online 在线状态
     * @param deviceType 设备类型
     * @param owner 所有者
     * @param pageable 分页参数
     * @return 设备分页列表
     */
    public Page<Device> findByConditions(Device.DeviceStatus status, Boolean online, 
                                        String deviceType, String owner, Pageable pageable) {
        return deviceRepository.findDevicesByConditions(status, online, deviceType, owner, pageable);
    }
    
    /**
     * 搜索设备
     * 
     * @param keyword 关键字
     * @param pageable 分页参数
     * @return 设备分页列表
     */
    public Page<Device> searchDevices(String keyword, Pageable pageable) {
        return deviceRepository.searchDevices(keyword, pageable);
    }
    
    /**
     * 获取在线设备数量
     * 
     * @return 在线设备数量
     */
    @Cacheable(value = "device:stats", key = "'online_count'")
    public long getOnlineDeviceCount() {
        return deviceRepository.countOnlineDevices();
    }
    
    /**
     * 获取离线设备数量
     * 
     * @return 离线设备数量
     */
    @Cacheable(value = "device:stats", key = "'offline_count'")
    public long getOfflineDeviceCount() {
        return deviceRepository.countOfflineDevices();
    }
    
    /**
     * 获取设备状态分布统计
     * 
     * @return 状态分布统计
     */
    @Cacheable(value = "device:stats", key = "'status_distribution'")
    public List<Object[]> getStatusDistribution() {
        return deviceRepository.getStatusDistribution();
    }
    
    /**
     * 获取设备类型分布统计
     * 
     * @return 类型分布统计
     */
    @Cacheable(value = "device:stats", key = "'type_distribution'")
    public List<Object[]> getDeviceTypeDistribution() {
        return deviceRepository.getDeviceTypeDistribution();
    }
    
    /**
     * 获取设备注册趋势
     * 
     * @param days 天数
     * @return 注册趋势统计
     */
    public List<Object[]> getRegistrationTrend(int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        return deviceRepository.getRegistrationTrend(startTime);
    }
    
    /**
     * 查找服务即将到期的设备
     * 
     * @param days 提前天数
     * @return 即将到期的设备列表
     */
    public List<Device> findDevicesExpiringSoon(int days) {
        LocalDateTime expireTime = LocalDateTime.now().plusDays(days);
        return deviceRepository.findDevicesWithExpiringSoon(expireTime);
    }
    
    /**
     * 查找长时间离线的设备
     * 
     * @param hours 离线小时数
     * @return 长时间离线的设备列表
     */
    public List<Device> findLongTimeOfflineDevices(int hours) {
        LocalDateTime offlineTime = LocalDateTime.now().minusHours(hours);
        return deviceRepository.findLongTimeOfflineDevices(offlineTime);
    }
    
    /**
     * 获取最近注册的设备
     * 
     * @param limit 限制数量
     * @return 最近注册的设备列表
     */
    public List<Device> getRecentRegisteredDevices(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return deviceRepository.findRecentRegisteredDevices(pageable);
    }
    
    /**
     * 检查IMEI是否存在
     * 
     * @param imei 设备IMEI
     * @return 是否存在
     */
    public boolean existsByImei(String imei) {
        return deviceRepository.existsByImei(imei);
    }
    
    /**
     * 检查车牌号是否存在
     * 
     * @param plateNumber 车牌号
     * @return 是否存在
     */
    public boolean existsByPlateNumber(String plateNumber) {
        return deviceRepository.existsByPlateNumber(plateNumber);
    }
}
