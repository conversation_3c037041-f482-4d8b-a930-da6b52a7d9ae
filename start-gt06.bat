@echo off
echo Starting GT06 System...

REM 设置JAVA_HOME（如果需要）
REM set JAVA_HOME=C:\Program Files\Java\jdk-21

REM 设置classpath
set CLASSPATH=gt06-web\target\classes
set CLASSPATH=%CLASSPATH%;gt06-network\target\classes
set CLASSPATH=%CLASSPATH%;gt06-service\target\classes
set CLASSPATH=%CLASSPATH%;gt06-protocol\target\classes
set CLASSPATH=%CLASSPATH%;gt06-common\target\classes

REM 添加所有依赖jar文件
for %%i in (gt06-web\target\lib\*.jar) do set CLASSPATH=%CLASSPATH%;%%i

echo Classpath: %CLASSPATH%
echo.

REM 启动应用
java -cp "%CLASSPATH%" com.gt06.web.GT06WebApplication

pause
