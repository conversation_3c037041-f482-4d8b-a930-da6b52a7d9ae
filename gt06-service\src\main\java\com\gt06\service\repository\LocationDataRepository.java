package com.gt06.service.repository;

import com.gt06.service.entity.LocationData;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 位置数据访问接口
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Repository
public interface LocationDataRepository extends JpaRepository<LocationData, Long> {
    
    /**
     * 根据IMEI查找最新位置
     * 
     * @param imei 设备IMEI
     * @return 最新位置数据
     */
    @Query("SELECT l FROM LocationData l WHERE l.imei = :imei ORDER BY l.locationTime DESC")
    Optional<LocationData> findLatestByImei(@Param("imei") String imei);
    
    /**
     * 根据IMEI和时间范围查找位置数据
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 位置数据列表
     */
    List<LocationData> findByImeiAndLocationTimeBetweenOrderByLocationTime(
        String imei, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据IMEI和时间范围分页查找位置数据
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 位置数据分页列表
     */
    Page<LocationData> findByImeiAndLocationTimeBetween(
        String imei, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据IMEI查找指定数量的最新位置数据
     * 
     * @param imei 设备IMEI
     * @param pageable 分页参数
     * @return 位置数据列表
     */
    @Query("SELECT l FROM LocationData l WHERE l.imei = :imei ORDER BY l.locationTime DESC")
    List<LocationData> findRecentLocationsByImei(@Param("imei") String imei, Pageable pageable);
    
    /**
     * 根据时间范围查找所有设备的位置数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 位置数据列表
     */
    List<LocationData> findByLocationTimeBetweenOrderByLocationTime(
        LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据地理范围查找位置数据
     * 
     * @param minLat 最小纬度
     * @param maxLat 最大纬度
     * @param minLng 最小经度
     * @param maxLng 最大经度
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 位置数据列表
     */
    @Query("SELECT l FROM LocationData l WHERE " +
           "l.latitude BETWEEN :minLat AND :maxLat AND " +
           "l.longitude BETWEEN :minLng AND :maxLng AND " +
           "l.locationTime BETWEEN :startTime AND :endTime AND " +
           "l.validLocation = true " +
           "ORDER BY l.locationTime DESC")
    List<LocationData> findByGeoBounds(@Param("minLat") BigDecimal minLat,
                                      @Param("maxLat") BigDecimal maxLat,
                                      @Param("minLng") BigDecimal minLng,
                                      @Param("maxLng") BigDecimal maxLng,
                                      @Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找超速记录
     * 
     * @param imei 设备IMEI
     * @param speedLimit 速度限制
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 超速记录列表
     */
    @Query("SELECT l FROM LocationData l WHERE " +
           "l.imei = :imei AND " +
           "l.speed > :speedLimit AND " +
           "l.locationTime BETWEEN :startTime AND :endTime " +
           "ORDER BY l.locationTime DESC")
    List<LocationData> findOverspeedRecords(@Param("imei") String imei,
                                           @Param("speedLimit") Integer speedLimit,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找停车记录
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 停车记录列表
     */
    @Query("SELECT l FROM LocationData l WHERE " +
           "l.imei = :imei AND " +
           "(l.speed = 0 OR l.speed IS NULL) AND " +
           "l.accOn = false AND " +
           "l.locationTime BETWEEN :startTime AND :endTime " +
           "ORDER BY l.locationTime DESC")
    List<LocationData> findParkingRecords(@Param("imei") String imei,
                                         @Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计设备的行驶里程
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 总里程
     */
    @Query("SELECT COALESCE(MAX(l.mileage) - MIN(l.mileage), 0) FROM LocationData l WHERE " +
           "l.imei = :imei AND " +
           "l.locationTime BETWEEN :startTime AND :endTime AND " +
           "l.mileage IS NOT NULL")
    BigDecimal calculateMileage(@Param("imei") String imei,
                               @Param("startTime") LocalDateTime startTime,
                               @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计设备的平均速度
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均速度
     */
    @Query("SELECT AVG(l.speed) FROM LocationData l WHERE " +
           "l.imei = :imei AND " +
           "l.locationTime BETWEEN :startTime AND :endTime AND " +
           "l.speed IS NOT NULL AND l.speed > 0")
    Double calculateAverageSpeed(@Param("imei") String imei,
                                @Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计设备的最高速度
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 最高速度
     */
    @Query("SELECT MAX(l.speed) FROM LocationData l WHERE " +
           "l.imei = :imei AND " +
           "l.locationTime BETWEEN :startTime AND :endTime AND " +
           "l.speed IS NOT NULL")
    Integer findMaxSpeed(@Param("imei") String imei,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计位置数据数量
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数据数量
     */
    long countByImeiAndLocationTimeBetween(String imei, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 删除过期的位置数据
     * 
     * @param expireTime 过期时间
     * @return 删除数量
     */
    @Modifying
    @Query("DELETE FROM LocationData l WHERE l.createTime < :expireTime")
    int deleteExpiredData(@Param("expireTime") LocalDateTime expireTime);
    
    /**
     * 根据IMEI删除位置数据
     * 
     * @param imei 设备IMEI
     * @return 删除数量
     */
    @Modifying
    int deleteByImei(String imei);
    
    /**
     * 查找轨迹数据（用于轨迹回放）
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 轨迹数据列表
     */
    @Query("SELECT l FROM LocationData l WHERE " +
           "l.imei = :imei AND " +
           "l.locationTime BETWEEN :startTime AND :endTime AND " +
           "l.validLocation = true AND " +
           "l.gpsFixed = true " +
           "ORDER BY l.locationTime ASC")
    List<LocationData> findTrackData(@Param("imei") String imei,
                                    @Param("startTime") LocalDateTime startTime,
                                    @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找所有设备的最新位置
     * 
     * @return 最新位置数据列表
     */
    @Query("SELECT l FROM LocationData l WHERE l.id IN (" +
           "SELECT MAX(l2.id) FROM LocationData l2 GROUP BY l2.imei" +
           ") ORDER BY l.locationTime DESC")
    List<LocationData> findLatestLocationForAllDevices();
    
    /**
     * 统计每日位置数据量
     * 
     * @param startTime 开始时间
     * @return 统计结果
     */
    @Query("SELECT DATE(l.createTime) as date, COUNT(l) as count " +
           "FROM LocationData l WHERE l.createTime >= :startTime " +
           "GROUP BY DATE(l.createTime) ORDER BY DATE(l.createTime)")
    List<Object[]> getDailyLocationDataCount(@Param("startTime") LocalDateTime startTime);
    
    /**
     * 统计设备活跃度
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @Query("SELECT l.imei, COUNT(l) as count " +
           "FROM LocationData l WHERE l.locationTime BETWEEN :startTime AND :endTime " +
           "GROUP BY l.imei ORDER BY COUNT(l) DESC")
    List<Object[]> getDeviceActivityStats(@Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找GPS定位率
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return GPS定位率
     */
    @Query("SELECT " +
           "CAST(SUM(CASE WHEN l.gpsFixed = true THEN 1 ELSE 0 END) AS DOUBLE) / COUNT(l) " +
           "FROM LocationData l WHERE " +
           "l.imei = :imei AND " +
           "l.locationTime BETWEEN :startTime AND :endTime")
    Double calculateGpsFixRate(@Param("imei") String imei,
                              @Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime);
}
