package com.gt06.test.client;

import com.gt06.protocol.message.*;
import com.gt06.protocol.util.CRCUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.Socket;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * GT06协议测试客户端
 * 模拟GPS设备与服务器的通信
 * 
 * <AUTHOR> Test
 * @version 1.0.0
 */
@Slf4j
public class GT06TestClient {
    
    private static final String DEFAULT_HOST = "localhost";
    private static final int DEFAULT_PORT = 8888;
    private static final int CONNECT_TIMEOUT = 5000;
    private static final int READ_TIMEOUT = 10000;
    
    private final String host;
    private final int port;
    private final String deviceId;
    
    private Socket socket;
    private DataInputStream inputStream;
    private DataOutputStream outputStream;
    private volatile boolean connected = false;
    private volatile boolean running = false;
    
    public GT06TestClient(String deviceId) {
        this(DEFAULT_HOST, DEFAULT_PORT, deviceId);
    }
    
    public GT06TestClient(String host, int port, String deviceId) {
        this.host = host;
        this.port = port;
        this.deviceId = deviceId;
    }
    
    /**
     * 连接到GT06服务器
     */
    public boolean connect() {
        try {
            log.info("🔌 Connecting to GT06 server {}:{} with device ID: {}", host, port, deviceId);
            
            socket = new Socket();
            socket.connect(new java.net.InetSocketAddress(host, port), CONNECT_TIMEOUT);
            socket.setSoTimeout(READ_TIMEOUT);
            
            inputStream = new DataInputStream(socket.getInputStream());
            outputStream = new DataOutputStream(socket.getOutputStream());
            
            connected = true;
            log.info("✅ Connected to GT06 server successfully");
            return true;
            
        } catch (Exception e) {
            log.error("❌ Failed to connect to GT06 server: {}", e.getMessage());
            disconnect();
            return false;
        }
    }
    
    /**
     * 断开连接
     */
    public void disconnect() {
        running = false;
        connected = false;
        
        try {
            if (inputStream != null) inputStream.close();
            if (outputStream != null) outputStream.close();
            if (socket != null) socket.close();
            log.info("🔌 Disconnected from GT06 server");
        } catch (Exception e) {
            log.warn("Error during disconnect: {}", e.getMessage());
        }
    }
    
    /**
     * 发送登录消息
     */
    public boolean sendLogin() {
        try {
            log.info("📤 Sending login message for device: {}", deviceId);
            
            LoginMessage loginMessage = new LoginMessage();
            loginMessage.setDeviceId(deviceId);
            loginMessage.setTimestamp(LocalDateTime.now());
            
            byte[] messageData = buildMessage((byte) 0x01, loginMessage.toBytes());
            outputStream.write(messageData);
            outputStream.flush();
            
            log.info("✅ Login message sent successfully");
            return true;
            
        } catch (Exception e) {
            log.error("❌ Failed to send login message: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 发送位置消息
     */
    public boolean sendLocation(double latitude, double longitude, int speed) {
        try {
            log.info("📍 Sending location message: lat={}, lng={}, speed={}", latitude, longitude, speed);
            
            LocationMessage locationMessage = new LocationMessage();
            locationMessage.setDeviceId(deviceId);
            locationMessage.setTimestamp(LocalDateTime.now());
            locationMessage.setLatitude(latitude);
            locationMessage.setLongitude(longitude);
            locationMessage.setSpeed(speed);
            locationMessage.setCourse(0);
            locationMessage.setStatus(0x00);
            
            byte[] messageData = buildMessage((byte) 0x12, locationMessage.toBytes());
            outputStream.write(messageData);
            outputStream.flush();
            
            log.info("✅ Location message sent successfully");
            return true;
            
        } catch (Exception e) {
            log.error("❌ Failed to send location message: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 发送心跳消息
     */
    public boolean sendHeartbeat() {
        try {
            log.info("💓 Sending heartbeat message");
            
            HeartbeatMessage heartbeatMessage = new HeartbeatMessage();
            heartbeatMessage.setDeviceId(deviceId);
            heartbeatMessage.setTimestamp(LocalDateTime.now());
            
            byte[] messageData = buildMessage((byte) 0x13, heartbeatMessage.toBytes());
            outputStream.write(messageData);
            outputStream.flush();
            
            log.info("✅ Heartbeat message sent successfully");
            return true;
            
        } catch (Exception e) {
            log.error("❌ Failed to send heartbeat message: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 构建完整的GT06消息
     */
    private byte[] buildMessage(byte messageType, byte[] content) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        
        try {
            // 起始位
            baos.write(0x78);
            baos.write(0x78);
            
            // 数据长度
            baos.write(content.length + 5); // content + 协议号 + 序列号 + CRC + 停止位
            
            // 协议号
            baos.write(messageType);
            
            // 数据内容
            baos.write(content);
            
            // 序列号 (简单递增)
            baos.write(0x00);
            baos.write(0x01);
            
            // 计算CRC
            byte[] dataForCrc = baos.toByteArray();
            byte[] crcData = new byte[dataForCrc.length - 2]; // 排除起始位
            System.arraycopy(dataForCrc, 2, crcData, 0, crcData.length);
            
            int crc = CRCUtil.calculateCRC(crcData);
            baos.write((crc >> 8) & 0xFF);
            baos.write(crc & 0xFF);
            
            // 停止位
            baos.write(0x0D);
            baos.write(0x0A);
            
            return baos.toByteArray();
            
        } catch (Exception e) {
            log.error("Failed to build message: {}", e.getMessage());
            return new byte[0];
        }
    }
    
    /**
     * 读取服务器响应
     */
    public byte[] readResponse() {
        try {
            if (!connected || inputStream.available() == 0) {
                return null;
            }
            
            // 读取起始位
            byte start1 = inputStream.readByte();
            byte start2 = inputStream.readByte();
            
            if (start1 != 0x78 || start2 != 0x78) {
                log.warn("Invalid start bytes: 0x{} 0x{}", 
                    Integer.toHexString(start1 & 0xFF), 
                    Integer.toHexString(start2 & 0xFF));
                return null;
            }
            
            // 读取数据长度
            int length = inputStream.readByte() & 0xFF;
            
            // 读取剩余数据
            byte[] data = new byte[length + 4]; // length + CRC + 停止位
            inputStream.readFully(data);
            
            log.info("📥 Received response from server: {} bytes", data.length + 3);
            return data;
            
        } catch (Exception e) {
            log.error("❌ Failed to read response: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 启动心跳线程
     */
    public void startHeartbeat(int intervalSeconds) {
        running = true;
        CompletableFuture.runAsync(() -> {
            while (running && connected) {
                try {
                    TimeUnit.SECONDS.sleep(intervalSeconds);
                    if (running && connected) {
                        sendHeartbeat();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    log.error("Heartbeat error: {}", e.getMessage());
                }
            }
        });
    }
    
    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return connected && socket != null && socket.isConnected() && !socket.isClosed();
    }
    
    /**
     * 获取设备ID
     */
    public String getDeviceId() {
        return deviceId;
    }
}
