package com.gt06.network.handler;

import com.gt06.common.constants.GT06Constants;
import com.gt06.network.session.DeviceSession;
import com.gt06.network.session.SessionManager;
import com.gt06.protocol.message.*;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * GT06消息处理器
 * 处理接收到的GT06协议消息
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Component
public class GT06MessageHandler extends SimpleChannelInboundHandler<GT06Message> {
    
    private final SessionManager sessionManager;
    
    public GT06MessageHandler(SessionManager sessionManager) {
        this.sessionManager = sessionManager;
    }
    
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, GT06Message msg) throws Exception {
        // 更新会话活动时间
        sessionManager.updateLastActivity(ctx.channel());
        
        // 获取会话信息
        DeviceSession session = sessionManager.getSession(ctx.channel());
        if (session != null) {
            session.incrementReceivedCount();
        }
        
        log.debug("Received message: {} from {}", 
            msg.getMessageDescription(), ctx.channel().remoteAddress());
        
        // 根据消息类型分发处理
        try {
            switch (msg.getProtocolNumber()) {
                case GT06Constants.PROTOCOL_LOGIN:
                    handleLoginMessage(ctx, (LoginMessage) msg);
                    break;
                    
                case GT06Constants.PROTOCOL_HEARTBEAT:
                    handleHeartbeatMessage(ctx, (HeartbeatMessage) msg);
                    break;
                    
                case GT06Constants.PROTOCOL_LOCATION:
                    handleLocationMessage(ctx, (LocationMessage) msg);
                    break;
                    
                case GT06Constants.PROTOCOL_ALARM:
                    handleAlarmMessage(ctx, (AlarmMessage) msg);
                    break;
                    
                case GT06Constants.PROTOCOL_COMMAND:
                    handleCommandMessage(ctx, (CommandMessage) msg);
                    break;
                    
                default:
                    handleUnknownMessage(ctx, msg);
                    break;
            }
        } catch (Exception e) {
            log.error("Error processing message: {} from {}", 
                msg.getMessageDescription(), ctx.channel().remoteAddress(), e);
            
            // 发送错误响应（如果需要）
            if (msg.needsResponse()) {
                sendErrorResponse(ctx, msg);
            }
        }
    }
    
    /**
     * 处理登录消息
     */
    private void handleLoginMessage(ChannelHandlerContext ctx, LoginMessage loginMsg) {
        String imei = loginMsg.getImei();
        
        log.info("Device login request: IMEI={} from {}", imei, ctx.channel().remoteAddress());
        
        // 验证IMEI
        if (imei == null || imei.length() != 15) {
            log.warn("Invalid IMEI: {} from {}", imei, ctx.channel().remoteAddress());
            ctx.close();
            return;
        }
        
        // 认证会话
        boolean authenticated = sessionManager.authenticateSession(ctx.channel(), imei);
        if (!authenticated) {
            log.warn("Failed to authenticate session for IMEI: {}", imei);
            ctx.close();
            return;
        }
        
        // 更新会话信息
        DeviceSession session = sessionManager.getSession(ctx.channel());
        if (session != null) {
            session.setDeviceType(loginMsg.getDeviceModel());
            session.setDeviceVersion("1.0.0"); // 默认版本，LoginMessage中没有软件版本字段
        }
        
        // 发送登录响应
        ResponseMessage response = ResponseMessage.createLoginResponse(loginMsg.getSerialNumber());
        sendResponse(ctx, response);
        
        log.info("Device authenticated successfully: IMEI={}", imei);
    }
    
    /**
     * 处理心跳消息
     */
    private void handleHeartbeatMessage(ChannelHandlerContext ctx, HeartbeatMessage heartbeatMsg) {
        // 检查会话是否已认证
        if (!sessionManager.isAuthenticated(ctx.channel())) {
            log.warn("Received heartbeat from unauthenticated session: {}", ctx.channel().remoteAddress());
            ctx.close();
            return;
        }
        
        // 更新会话心跳时间
        DeviceSession session = sessionManager.getSession(ctx.channel());
        if (session != null) {
            session.updateHeartbeat();
            session.setSignalStrength(heartbeatMsg.getGsmSignalStrength());
            session.setBatteryVoltage(heartbeatMsg.getVoltageLevel());
            session.setAccOn(heartbeatMsg.isAccOn());
        }
        
        // 发送心跳响应
        ResponseMessage response = ResponseMessage.createHeartbeatResponse(heartbeatMsg.getSerialNumber());
        sendResponse(ctx, response);
        
        log.debug("Heartbeat processed for IMEI: {}", session != null ? session.getImei() : "unknown");
    }
    
    /**
     * 处理位置消息
     */
    private void handleLocationMessage(ChannelHandlerContext ctx, LocationMessage locationMsg) {
        // 检查会话是否已认证
        if (!sessionManager.isAuthenticated(ctx.channel())) {
            log.warn("Received location from unauthenticated session: {}", ctx.channel().remoteAddress());
            ctx.close();
            return;
        }
        
        // 更新会话位置信息
        DeviceSession session = sessionManager.getSession(ctx.channel());
        if (session != null) {
            session.updateLocation(
                locationMsg.getLatitude(),
                locationMsg.getLongitude(),
                locationMsg.getSpeed(),
                locationMsg.getDirection(),
                locationMsg.isGpsFixed()
            );
            session.setAccOn(locationMsg.isAccOn());
        }
        
        // 发送位置响应
        ResponseMessage response = ResponseMessage.createLocationResponse(locationMsg.getSerialNumber());
        sendResponse(ctx, response);
        
        log.debug("Location updated for IMEI: {} - GPS({}, {})", 
            session != null ? session.getImei() : "unknown",
            locationMsg.getLatitude(), locationMsg.getLongitude());
        
        // TODO: 将位置数据保存到数据库或发送到消息队列
    }
    
    /**
     * 处理报警消息
     */
    private void handleAlarmMessage(ChannelHandlerContext ctx, AlarmMessage alarmMsg) {
        // 检查会话是否已认证
        if (!sessionManager.isAuthenticated(ctx.channel())) {
            log.warn("Received alarm from unauthenticated session: {}", ctx.channel().remoteAddress());
            ctx.close();
            return;
        }
        
        DeviceSession session = sessionManager.getSession(ctx.channel());
        String imei = session != null ? session.getImei() : "unknown";
        
        // 记录报警信息
        log.warn("ALARM received from IMEI: {} - Type: {} Level: {} Location: {}", 
            imei, 
            alarmMsg.getAlarmType().getDescription(),
            alarmMsg.getAlarmLevel().getDescription(),
            alarmMsg.getLocationDescription());
        
        // 更新会话状态
        if (session != null) {
            session.setDeviceStatus(DeviceSession.DeviceStatus.ALARM);
            
            // 如果有位置信息，更新会话位置
            if (alarmMsg.isGpsFixed()) {
                session.updateLocation(
                    alarmMsg.getLatitude(),
                    alarmMsg.getLongitude(),
                    alarmMsg.getSpeed(),
                    alarmMsg.getDirection(),
                    alarmMsg.isGpsFixed()
                );
            }
        }
        
        // 发送报警响应
        ResponseMessage response = ResponseMessage.createAlarmResponse(alarmMsg.getSerialNumber());
        sendResponse(ctx, response);
        
        // TODO: 处理紧急报警，发送通知等
        if (alarmMsg.isCriticalAlarm()) {
            handleCriticalAlarm(session, alarmMsg);
        }
    }
    
    /**
     * 处理指令消息
     */
    private void handleCommandMessage(ChannelHandlerContext ctx, CommandMessage commandMsg) {
        log.info("Command message received: {} from {}", 
            commandMsg.getCommandContent(), ctx.channel().remoteAddress());
        
        // 指令消息通常是服务器发送给设备的，这里收到可能是设备的响应
        // 具体处理逻辑根据业务需求实现
        
        // TODO: 处理指令响应或设备主动上报的指令状态
    }
    
    /**
     * 处理未知消息
     */
    private void handleUnknownMessage(ChannelHandlerContext ctx, GT06Message msg) {
        log.warn("Unknown message type: 0x{} from {}", 
            String.format("%02X", msg.getProtocolNumber() & 0xFF),
            ctx.channel().remoteAddress());
        
        // 对于未知消息，通常不发送响应
    }
    
    /**
     * 处理紧急报警
     */
    private void handleCriticalAlarm(DeviceSession session, AlarmMessage alarmMsg) {
        log.error("CRITICAL ALARM: IMEI={} Type={} Location={}", 
            session != null ? session.getImei() : "unknown",
            alarmMsg.getAlarmType().getDescription(),
            alarmMsg.getLocationDescription());
        
        // TODO: 实现紧急报警处理逻辑
        // 1. 发送短信/邮件通知
        // 2. 推送到监控中心
        // 3. 记录到报警日志
        // 4. 触发自动响应流程
    }
    
    /**
     * 发送响应消息
     */
    private void sendResponse(ChannelHandlerContext ctx, GT06Message response) {
        if (response != null) {
            ctx.writeAndFlush(response).addListener(future -> {
                if (future.isSuccess()) {
                    DeviceSession session = sessionManager.getSession(ctx.channel());
                    if (session != null) {
                        session.incrementSentCount();
                    }
                    
                    log.debug("Response sent: {} to {}", 
                        response.getMessageDescription(), ctx.channel().remoteAddress());
                } else {
                    log.error("Failed to send response: {} to {}", 
                        response.getMessageDescription(), ctx.channel().remoteAddress(), 
                        future.cause());
                }
            });
        }
    }
    
    /**
     * 发送错误响应
     */
    private void sendErrorResponse(ChannelHandlerContext ctx, GT06Message originalMsg) {
        try {
            ResponseMessage errorResponse = ResponseMessage.createErrorResponse(
                originalMsg.getProtocolNumber(), 
                originalMsg.getSerialNumber()
            );
            sendResponse(ctx, errorResponse);
        } catch (Exception e) {
            log.error("Failed to send error response", e);
        }
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        log.error("Exception in GT06MessageHandler for channel {}", 
            ctx.channel().remoteAddress(), cause);
        
        // 关闭连接
        ctx.close();
    }
}
