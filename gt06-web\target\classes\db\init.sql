-- GT06协议系统数据库初始化脚本
-- 创建数据库和用户

-- 创建数据库
CREATE DATABASE IF NOT EXISTS gt06_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS gt06_system_dev DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS gt06_system_test DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER IF NOT EXISTS 'gt06_user'@'%' IDENTIFIED BY 'gt06_password';
CREATE USER IF NOT EXISTS 'gt06_user'@'localhost' IDENTIFIED BY 'gt06_password';

-- 授权
GRANT ALL PRIVILEGES ON gt06_system.* TO 'gt06_user'@'%';
GRANT ALL PRIVILEGES ON gt06_system_dev.* TO 'gt06_user'@'%';
GRANT ALL PRIVILEGES ON gt06_system_test.* TO 'gt06_user'@'%';
GRANT ALL PRIVILEGES ON gt06_system.* TO 'gt06_user'@'localhost';
GRANT ALL PRIVILEGES ON gt06_system_dev.* TO 'gt06_user'@'localhost';
GRANT ALL PRIVILEGES ON gt06_system_test.* TO 'gt06_user'@'localhost';

FLUSH PRIVILEGES;

-- 使用开发数据库
USE gt06_system_dev;

-- 设备表
CREATE TABLE IF NOT EXISTS device (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    imei VARCHAR(15) NOT NULL UNIQUE COMMENT '设备IMEI',
    device_name VARCHAR(100) COMMENT '设备名称',
    device_type VARCHAR(50) COMMENT '设备类型',
    device_model VARCHAR(50) COMMENT '设备型号',
    software_version VARCHAR(50) COMMENT '软件版本',
    hardware_version VARCHAR(50) COMMENT '硬件版本',
    status VARCHAR(20) NOT NULL DEFAULT 'INACTIVE' COMMENT '设备状态',
    online BOOLEAN DEFAULT FALSE COMMENT '是否在线',
    last_online_time DATETIME COMMENT '最后在线时间',
    last_offline_time DATETIME COMMENT '最后离线时间',
    register_time DATETIME COMMENT '注册时间',
    sim_number VARCHAR(20) COMMENT 'SIM卡号',
    owner VARCHAR(100) COMMENT '所有者',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    plate_number VARCHAR(20) COMMENT '车牌号',
    vehicle_type VARCHAR(50) COMMENT '车辆类型',
    vehicle_brand VARCHAR(50) COMMENT '车辆品牌',
    vehicle_color VARCHAR(20) COMMENT '车辆颜色',
    install_location VARCHAR(100) COMMENT '安装位置',
    install_time DATETIME COMMENT '安装时间',
    service_expire_time DATETIME COMMENT '服务到期时间',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    remarks TEXT COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_imei (imei),
    INDEX idx_status (status),
    INDEX idx_online (online),
    INDEX idx_plate_number (plate_number),
    INDEX idx_owner (owner),
    INDEX idx_create_time (create_time),
    INDEX idx_service_expire_time (service_expire_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备信息表';

-- 位置数据表
CREATE TABLE IF NOT EXISTS location_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    imei VARCHAR(15) NOT NULL COMMENT '设备IMEI',
    location_time DATETIME NOT NULL COMMENT '定位时间',
    latitude DECIMAL(10,7) COMMENT '纬度',
    longitude DECIMAL(10,7) COMMENT '经度',
    altitude INT COMMENT '海拔高度',
    speed INT COMMENT '速度',
    direction INT COMMENT '方向角',
    satellites INT COMMENT '卫星数',
    gps_fixed BOOLEAN COMMENT 'GPS是否定位',
    location_type VARCHAR(10) DEFAULT 'GPS' COMMENT '定位类型',
    mcc INT COMMENT '移动国家代码',
    mnc INT COMMENT '移动网络代码',
    lac INT COMMENT '位置区域码',
    cell_id BIGINT COMMENT '基站ID',
    signal_strength INT COMMENT '信号强度',
    battery_level INT COMMENT '电池电量',
    gsm_signal INT COMMENT 'GSM信号强度',
    mileage DECIMAL(10,2) COMMENT '里程',
    address VARCHAR(200) COMMENT '地址',
    valid_location BOOLEAN DEFAULT TRUE COMMENT '是否有效位置',
    raw_data TEXT COMMENT '原始数据',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_imei_time (imei, location_time),
    INDEX idx_location_time (location_time),
    INDEX idx_gps_fixed (gps_fixed),
    INDEX idx_location_type (location_type),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='位置数据表';

-- 报警数据表
CREATE TABLE IF NOT EXISTS alarm_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    imei VARCHAR(15) NOT NULL COMMENT '设备IMEI',
    alarm_type VARCHAR(50) NOT NULL COMMENT '报警类型',
    alarm_level VARCHAR(20) NOT NULL COMMENT '报警级别',
    alarm_time DATETIME NOT NULL COMMENT '报警时间',
    alarm_description VARCHAR(200) COMMENT '报警描述',
    latitude DECIMAL(10,7) COMMENT '纬度',
    longitude DECIMAL(10,7) COMMENT '经度',
    address VARCHAR(200) COMMENT '地址',
    gps_fixed BOOLEAN COMMENT 'GPS是否定位',
    speed INT COMMENT '速度',
    direction INT COMMENT '方向角',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '处理状态',
    handle_time DATETIME COMMENT '处理时间',
    handler VARCHAR(100) COMMENT '处理人',
    handle_remarks TEXT COMMENT '处理备注',
    notified BOOLEAN DEFAULT FALSE COMMENT '是否已通知',
    notify_time DATETIME COMMENT '通知时间',
    notify_method VARCHAR(50) COMMENT '通知方式',
    raw_data TEXT COMMENT '原始数据',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_imei_time (imei, alarm_time),
    INDEX idx_alarm_type (alarm_type),
    INDEX idx_alarm_level (alarm_level),
    INDEX idx_status (status),
    INDEX idx_alarm_time (alarm_time),
    INDEX idx_notified (notified),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报警数据表';

-- 插入测试数据
INSERT INTO device (imei, device_name, device_type, device_model, software_version, hardware_version, 
                   status, online, register_time, sim_number, owner, contact_phone, plate_number, 
                   vehicle_type, vehicle_brand, vehicle_color, install_location, install_time, 
                   service_expire_time, enabled, remarks) VALUES
('123456789012345', '测试设备001', 'GT06N', 'GT06N-V2.0', '1.0.0', '2.0', 'ACTIVE', TRUE, 
 NOW(), '13800138001', '张三', '13800138001', '京A12345', '小型汽车', '丰田', '白色', 
 '仪表台下方', NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), TRUE, '测试设备'),
('123456789012346', '测试设备002', 'GT06N', 'GT06N-V2.0', '1.0.0', '2.0', 'ACTIVE', FALSE, 
 NOW(), '13800138002', '李四', '13800138002', '京B67890', '小型汽车', '本田', '黑色', 
 '仪表台下方', NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), TRUE, '测试设备'),
('123456789012347', '测试设备003', 'GT06N', 'GT06N-V2.0', '1.0.0', '2.0', 'ACTIVE', TRUE, 
 NOW(), '13800138003', '王五', '13800138003', '京C11111', '小型汽车', '大众', '红色', 
 '仪表台下方', NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), TRUE, '测试设备');

-- 插入位置数据
INSERT INTO location_data (imei, location_time, latitude, longitude, altitude, speed, direction, 
                          satellites, gps_fixed, location_type, battery_level, gsm_signal, 
                          mileage, address, valid_location) VALUES
('123456789012345', NOW(), 39.9042, 116.4074, 50, 60, 90, 8, TRUE, 'GPS', 80, 25, 1000.50, 
 '北京市朝阳区', TRUE),
('123456789012346', DATE_SUB(NOW(), INTERVAL 1 HOUR), 39.9142, 116.4174, 45, 0, 0, 6, TRUE, 'GPS', 
 75, 23, 950.30, '北京市海淀区', TRUE),
('123456789012347', DATE_SUB(NOW(), INTERVAL 30 MINUTE), 39.8942, 116.3974, 55, 45, 180, 7, TRUE, 'GPS', 
 90, 28, 1200.80, '北京市西城区', TRUE);

-- 插入报警数据
INSERT INTO alarm_data (imei, alarm_type, alarm_level, alarm_time, alarm_description, latitude, 
                       longitude, address, gps_fixed, speed, direction, status, notified) VALUES
('123456789012345', 'OVERSPEED', 'WARNING', DATE_SUB(NOW(), INTERVAL 2 HOUR), '超速报警', 
 39.9042, 116.4074, '北京市朝阳区', TRUE, 85, 90, 'PENDING', FALSE),
('123456789012346', 'LOW_BATTERY', 'INFO', DATE_SUB(NOW(), INTERVAL 1 HOUR), '低电量报警', 
 39.9142, 116.4174, '北京市海淀区', TRUE, 0, 0, 'HANDLED', TRUE),
('123456789012347', 'EMERGENCY', 'CRITICAL', DATE_SUB(NOW(), INTERVAL 30 MINUTE), '紧急报警', 
 39.8942, 116.3974, '北京市西城区', TRUE, 45, 180, 'PENDING', FALSE);

-- 创建视图
CREATE OR REPLACE VIEW device_summary AS
SELECT 
    d.imei,
    d.device_name,
    d.status,
    d.online,
    d.last_online_time,
    d.plate_number,
    d.owner,
    l.location_time AS last_location_time,
    l.latitude,
    l.longitude,
    l.address,
    COUNT(a.id) AS alarm_count
FROM device d
LEFT JOIN (
    SELECT imei, MAX(location_time) AS max_time
    FROM location_data
    GROUP BY imei
) latest ON d.imei = latest.imei
LEFT JOIN location_data l ON d.imei = l.imei AND l.location_time = latest.max_time
LEFT JOIN alarm_data a ON d.imei = a.imei AND a.alarm_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY d.imei, d.device_name, d.status, d.online, d.last_online_time, d.plate_number, 
         d.owner, l.location_time, l.latitude, l.longitude, l.address;

-- 创建存储过程：清理过期数据
DELIMITER //
CREATE PROCEDURE CleanExpiredData(IN location_retention_days INT, IN alarm_retention_days INT)
BEGIN
    DECLARE location_expire_time DATETIME;
    DECLARE alarm_expire_time DATETIME;
    DECLARE location_deleted INT DEFAULT 0;
    DECLARE alarm_deleted INT DEFAULT 0;
    
    SET location_expire_time = DATE_SUB(NOW(), INTERVAL location_retention_days DAY);
    SET alarm_expire_time = DATE_SUB(NOW(), INTERVAL alarm_retention_days DAY);
    
    -- 删除过期位置数据
    DELETE FROM location_data WHERE create_time < location_expire_time;
    SET location_deleted = ROW_COUNT();
    
    -- 删除过期报警数据
    DELETE FROM alarm_data WHERE create_time < alarm_expire_time;
    SET alarm_deleted = ROW_COUNT();
    
    -- 记录清理结果
    SELECT CONCAT('清理完成: 删除位置数据 ', location_deleted, ' 条, 删除报警数据 ', alarm_deleted, ' 条') AS result;
END //
DELIMITER ;

-- 创建事件调度器：每日清理过期数据
SET GLOBAL event_scheduler = ON;

CREATE EVENT IF NOT EXISTS daily_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURRENT_DATE + INTERVAL 1 DAY, '02:00:00')
DO
  CALL CleanExpiredData(365, 180);

COMMIT;
