package com.gt06.protocol.message;

import com.gt06.common.constants.GT06Constants;
import com.gt06.common.util.ByteUtil;
import io.netty.buffer.ByteBuf;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * GT06协议报警消息 (0x16)
 * 设备向服务器发送的各种报警信息
 * 
 * 消息格式：
 * 起始位(2) + 包长度(1) + 协议号(1) + 日期时间(6) + GPS信息长度(1) + GPS信息(N) + 
 * LBS信息长度(1) + LBS信息(N) + 状态信息(1) + 序列号(2) + CRC(2) + 停止位(2)
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Getter
@Setter
public class AlarmMessage extends GT06Message {
    
    /** 报警时间 */
    private LocalDateTime alarmDateTime;
    
    /** 报警类型 */
    private AlarmType alarmType;
    
    /** 报警状态字节 */
    private byte alarmStatus;
    
    /** GPS定位信息（继承自LocationMessage的字段） */
    private double latitude;
    private double longitude;
    private int speed;
    private int direction;
    private boolean gpsFixed;
    private boolean accOn;
    
    /** LBS信息 */
    private int mcc;
    private int mnc;
    private int lac;
    private int cellId;
    private int signalStrength;
    
    /** 报警级别 */
    private AlarmLevel alarmLevel;
    
    /** 报警描述 */
    private String alarmDescription;
    
    /**
     * 报警类型枚举
     */
    public enum AlarmType {
        NORMAL(GT06Constants.ALARM_NORMAL, "正常", AlarmLevel.INFO),
        SOS(GT06Constants.ALARM_SOS, "SOS紧急报警", AlarmLevel.CRITICAL),
        POWER_OFF(GT06Constants.ALARM_POWER_OFF, "断电报警", AlarmLevel.HIGH),
        VIBRATION(GT06Constants.ALARM_VIBRATION, "震动报警", AlarmLevel.MEDIUM),
        FENCE_IN(GT06Constants.ALARM_FENCE_IN, "进围栏报警", AlarmLevel.MEDIUM),
        FENCE_OUT(GT06Constants.ALARM_FENCE_OUT, "出围栏报警", AlarmLevel.MEDIUM),
        OVERSPEED(GT06Constants.ALARM_OVERSPEED, "超速报警", AlarmLevel.HIGH),
        TAMPER(GT06Constants.ALARM_TAMPER, "防拆报警", AlarmLevel.HIGH),
        RAPID_ACCELERATION(GT06Constants.ALARM_RAPID_ACCELERATION, "急加速报警", AlarmLevel.MEDIUM),
        RAPID_DECELERATION(GT06Constants.ALARM_RAPID_DECELERATION, "急减速报警", AlarmLevel.MEDIUM),
        SHARP_TURN(GT06Constants.ALARM_SHARP_TURN, "急转弯报警", AlarmLevel.MEDIUM),
        COLLISION(GT06Constants.ALARM_COLLISION, "碰撞报警", AlarmLevel.CRITICAL);
        
        private final byte code;
        private final String description;
        private final AlarmLevel level;
        
        AlarmType(byte code, String description, AlarmLevel level) {
            this.code = code;
            this.description = description;
            this.level = level;
        }
        
        public byte getCode() { return code; }
        public String getDescription() { return description; }
        public AlarmLevel getLevel() { return level; }
        
        public static AlarmType fromCode(byte code) {
            for (AlarmType type : values()) {
                if (type.code == code) {
                    return type;
                }
            }
            return NORMAL;
        }
    }
    
    /**
     * 报警级别枚举
     */
    public enum AlarmLevel {
        INFO(0, "信息"),
        LOW(1, "低级"),
        MEDIUM(2, "中级"),
        HIGH(3, "高级"),
        CRITICAL(4, "紧急");
        
        private final int level;
        private final String description;
        
        AlarmLevel(int level, String description) {
            this.level = level;
            this.description = description;
        }
        
        public int getLevel() { return level; }
        public String getDescription() { return description; }
    }
    
    /**
     * 构造函数
     */
    public AlarmMessage() {
        super(GT06Constants.PROTOCOL_ALARM);
        this.alarmType = AlarmType.NORMAL;
        this.alarmLevel = AlarmLevel.INFO;
    }
    
    /**
     * 构造函数
     * 
     * @param alarmType 报警类型
     */
    public AlarmMessage(AlarmType alarmType) {
        super(GT06Constants.PROTOCOL_ALARM);
        this.alarmType = alarmType;
        this.alarmLevel = alarmType.getLevel();
        this.alarmDescription = alarmType.getDescription();
    }
    
    @Override
    public byte[] encode() {
        // 编码日期时间（6字节）
        byte[] dateTimeBytes = ByteUtil.dateTimeToBytes(alarmDateTime);
        
        // 编码GPS信息
        byte[] gpsInfo = encodeGpsInfo();
        
        // 编码LBS信息
        byte[] lbsInfo = encodeLbsInfo();
        
        // 状态信息（1字节）
        byte[] statusInfo = new byte[]{alarmStatus};
        
        // 计算内容长度
        int contentLength = 6 + 1 + gpsInfo.length + 1 + lbsInfo.length + 1;
        
        // 创建包头
        byte[] header = createPacketHeader(contentLength);
        
        // 组装内容
        byte[] content = ByteUtil.concat(
            dateTimeBytes,
            new byte[]{(byte) gpsInfo.length},
            gpsInfo,
            new byte[]{(byte) lbsInfo.length},
            lbsInfo,
            statusInfo
        );
        
        // 组装完整数据包（不包含CRC和停止位）
        byte[] dataForCRC = ByteUtil.concat(header, content);
        
        // 创建包尾
        byte[] tail = createPacketTail(dataForCRC);
        
        // 返回完整数据包
        return ByteUtil.concat(dataForCRC, tail);
    }
    
    @Override
    public void decode(ByteBuf buffer) {
        // 读取包头
        readPacketHeader(buffer);
        
        // 读取报警时间（6字节）
        this.alarmDateTime = ByteUtil.bytesToDateTime(buffer.array(), buffer.readerIndex());
        buffer.skipBytes(6);
        
        // 读取GPS信息
        int gpsInfoLength = buffer.readUnsignedByte();
        if (gpsInfoLength > 0) {
            decodeGpsInfo(buffer, gpsInfoLength);
        }
        
        // 读取LBS信息
        int lbsInfoLength = buffer.readUnsignedByte();
        if (lbsInfoLength > 0) {
            decodeLbsInfo(buffer, lbsInfoLength);
        }
        
        // 读取状态信息
        this.alarmStatus = buffer.readByte();
        parseAlarmStatus(this.alarmStatus);
        
        // 读取包尾
        readPacketTail(buffer);
    }
    
    /**
     * 编码GPS信息
     */
    private byte[] encodeGpsInfo() {
        if (!gpsFixed || latitude == 0 || longitude == 0) {
            return new byte[0];
        }
        
        byte[] gpsData = new byte[13];
        
        // 编码纬度和经度
        int latValue = (int) (latitude * 30000.0 / 180.0 * 60.0);
        int lngValue = (int) (longitude * 30000.0 / 180.0 * 60);
        
        System.arraycopy(ByteUtil.intToBytes4(latValue), 0, gpsData, 0, 4);
        System.arraycopy(ByteUtil.intToBytes4(lngValue), 0, gpsData, 4, 4);
        
        gpsData[8] = (byte) speed;
        
        int directionAndStatus = direction & 0x03FF;
        if (gpsFixed) directionAndStatus |= 0x1000;
        if (accOn) directionAndStatus |= 0x4000;
        
        System.arraycopy(ByteUtil.intToBytes2(directionAndStatus), 0, gpsData, 9, 2);
        System.arraycopy(ByteUtil.intToBytes2(0), 0, gpsData, 11, 2); // 海拔
        
        return gpsData;
    }
    
    /**
     * 编码LBS信息
     */
    private byte[] encodeLbsInfo() {
        if (mcc == 0 && mnc == 0 && lac == 0 && cellId == 0) {
            return new byte[0];
        }
        
        byte[] lbsData = new byte[9];
        
        System.arraycopy(ByteUtil.intToBytes2(mcc), 0, lbsData, 0, 2);
        lbsData[2] = (byte) mnc;
        System.arraycopy(ByteUtil.intToBytes2(lac), 0, lbsData, 3, 2);
        
        byte[] cellIdBytes = ByteUtil.intToBytes4(cellId);
        System.arraycopy(cellIdBytes, 1, lbsData, 5, 3);
        
        lbsData[8] = (byte) signalStrength;
        
        return lbsData;
    }
    
    /**
     * 解码GPS信息
     */
    private void decodeGpsInfo(ByteBuf buffer, int length) {
        if (length < 13) {
            buffer.skipBytes(length);
            return;
        }
        
        int latValue = buffer.readInt();
        this.latitude = latValue * 180.0 / 30000.0 / 60.0;
        
        int lngValue = buffer.readInt();
        this.longitude = lngValue * 180.0 / 30000.0 / 60.0;
        
        this.speed = buffer.readUnsignedByte();
        
        int directionAndStatus = buffer.readUnsignedShort();
        this.direction = directionAndStatus & 0x03FF;
        this.gpsFixed = (directionAndStatus & 0x1000) != 0;
        this.accOn = (directionAndStatus & 0x4000) != 0;
        
        buffer.skipBytes(2); // 跳过海拔
        
        if (length > 13) {
            buffer.skipBytes(length - 13);
        }
    }
    
    /**
     * 解码LBS信息
     */
    private void decodeLbsInfo(ByteBuf buffer, int length) {
        if (length < 9) {
            buffer.skipBytes(length);
            return;
        }
        
        this.mcc = buffer.readUnsignedShort();
        this.mnc = buffer.readUnsignedByte();
        this.lac = buffer.readUnsignedShort();
        this.cellId = ByteUtil.bytes3ToInt(buffer.array(), buffer.readerIndex());
        buffer.skipBytes(3);
        this.signalStrength = buffer.readUnsignedByte();
        
        if (length > 9) {
            buffer.skipBytes(length - 9);
        }
    }
    
    /**
     * 解析报警状态字节
     */
    private void parseAlarmStatus(byte status) {
        this.alarmType = AlarmType.fromCode(status);
        this.alarmLevel = this.alarmType.getLevel();
        this.alarmDescription = this.alarmType.getDescription();
    }
    
    @Override
    public boolean validate() {
        if (protocolNumber != GT06Constants.PROTOCOL_ALARM) {
            return false;
        }
        
        if (alarmDateTime == null) {
            return false;
        }
        
        if (alarmType == null) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public String getMessageDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("ALARM - ").append(alarmType.getDescription());
        desc.append(" [").append(alarmLevel.getDescription()).append("]");
        
        if (gpsFixed) {
            desc.append(String.format(" GPS(%.6f,%.6f)", latitude, longitude));
        }
        
        if (mcc != 0) {
            desc.append(String.format(" LBS(MCC:%d LAC:%d)", mcc, lac));
        }
        
        return desc.toString();
    }
    
    @Override
    public GT06Message createResponse() {
        return ResponseMessage.createAlarmResponse(this.serialNumber);
    }
    
    /**
     * 检查是否为紧急报警
     */
    public boolean isCriticalAlarm() {
        return alarmLevel == AlarmLevel.CRITICAL;
    }
    
    /**
     * 检查是否需要立即处理
     */
    public boolean needsImmediateAction() {
        return alarmLevel.getLevel() >= AlarmLevel.HIGH.getLevel();
    }
    
    /**
     * 获取报警的地理位置描述
     */
    public String getLocationDescription() {
        if (gpsFixed && latitude != 0 && longitude != 0) {
            return String.format("GPS: %.6f, %.6f", latitude, longitude);
        } else if (mcc != 0 && lac != 0) {
            return String.format("LBS: MCC=%d, LAC=%d, CellID=%d", mcc, lac, cellId);
        } else {
            return "位置信息不可用";
        }
    }
}
