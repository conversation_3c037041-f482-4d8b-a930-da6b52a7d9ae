import java.io.IOException;
import java.net.Socket;

/**
 * 简单的TCP服务器连接测试
 */
public class TestTcpServer {
    public static void main(String[] args) {
        String host = "localhost";
        int port = 8888;
        
        System.out.println("🔍 Testing GT06 TCP server connection...");
        System.out.println("Target: " + host + ":" + port);
        
        try (Socket socket = new Socket(host, port)) {
            System.out.println("✅ Successfully connected to GT06 TCP server!");
            System.out.println("Local address: " + socket.getLocalSocketAddress());
            System.out.println("Remote address: " + socket.getRemoteSocketAddress());
            
            // 保持连接几秒钟
            Thread.sleep(3000);
            
        } catch (IOException e) {
            System.err.println("❌ Failed to connect to GT06 TCP server: " + e.getMessage());
            System.err.println("Possible reasons:");
            System.err.println("  1. GT06 TCP server is not running");
            System.err.println("  2. Server is running on different port");
            System.err.println("  3. Firewall blocking connection");
        } catch (InterruptedException e) {
            System.err.println("Test interrupted");
        }
    }
}
