package com.gt06.web.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 设备统计数据传输对象
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "设备统计信息")
public class DeviceStatsDTO {
    
    @Schema(description = "在线设备数量", example = "150")
    private Long onlineCount;
    
    @Schema(description = "离线设备数量", example = "25")
    private Long offlineCount;
    
    @Schema(description = "总设备数量", example = "175")
    private Long totalCount;
    
    @Schema(description = "在线率", example = "0.857")
    private Double onlineRate;
    
    @Schema(description = "设备状态分布")
    private List<Object[]> statusDistribution;
    
    @Schema(description = "设备类型分布")
    private List<Object[]> typeDistribution;
    
    /**
     * 计算总数和在线率
     */
    public void calculateStats() {
        if (onlineCount != null && offlineCount != null) {
            this.totalCount = onlineCount + offlineCount;
            if (totalCount > 0) {
                this.onlineRate = (double) onlineCount / totalCount;
            } else {
                this.onlineRate = 0.0;
            }
        }
    }
    
    /**
     * 设置在线设备数量并计算统计
     */
    public void setOnlineCount(Long onlineCount) {
        this.onlineCount = onlineCount;
        calculateStats();
    }
    
    /**
     * 设置离线设备数量并计算统计
     */
    public void setOfflineCount(Long offlineCount) {
        this.offlineCount = offlineCount;
        calculateStats();
    }
}
