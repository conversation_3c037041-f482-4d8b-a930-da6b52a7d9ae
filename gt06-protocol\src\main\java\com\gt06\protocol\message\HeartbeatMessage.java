package com.gt06.protocol.message;

import com.gt06.common.constants.GT06Constants;
import com.gt06.common.util.ByteUtil;
import io.netty.buffer.ByteBuf;
import lombok.Getter;
import lombok.Setter;

/**
 * GT06协议心跳消息 (0x13)
 * 设备与服务器之间的保活消息
 * 
 * 消息格式：
 * 起始位(2) + 包长度(1) + 协议号(1) + 终端信息(1) + 电压等级(1) + GSM信号强度(1) + 
 * 语言(1) + 序列号(2) + CRC(2) + 停止位(2)
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Getter
@Setter
public class HeartbeatMessage extends GT06Message {
    
    /** 终端信息字节 */
    private byte terminalInfo;
    
    /** 电压等级 */
    private int voltageLevel;
    
    /** GSM信号强度 */
    private int gsmSignalStrength;
    
    /** 语言设置 */
    private byte language;
    
    /** 是否为油电断开状态 */
    private boolean oilElectricityDisconnected;
    
    /** 是否为GPS跟踪状态 */
    private boolean gpsTracking;
    
    /** 是否为充电状态 */
    private boolean charging;
    
    /** 是否为ACC开启状态 */
    private boolean accOn;
    
    /** 是否为布防状态 */
    private boolean armed;
    
    /**
     * 构造函数
     */
    public HeartbeatMessage() {
        super(GT06Constants.PROTOCOL_HEARTBEAT);
    }
    
    @Override
    public byte[] encode() {
        // 心跳消息内容：终端信息(1) + 电压等级(1) + GSM信号强度(1) + 语言(1)
        byte[] content = new byte[4];
        
        // 组装终端信息字节
        content[0] = buildTerminalInfoByte();
        content[1] = (byte) voltageLevel;
        content[2] = (byte) gsmSignalStrength;
        content[3] = language;
        
        // 创建包头
        byte[] header = createPacketHeader(content.length);
        
        // 组装完整数据包（不包含CRC和停止位）
        byte[] dataForCRC = ByteUtil.concat(header, content);
        
        // 创建包尾
        byte[] tail = createPacketTail(dataForCRC);
        
        // 返回完整数据包
        return ByteUtil.concat(dataForCRC, tail);
    }
    
    @Override
    public void decode(ByteBuf buffer) {
        // 读取包头
        readPacketHeader(buffer);
        
        // 计算内容长度
        int contentLength = length - 1 - 2 - 2; // 总长度 - 协议号 - 序列号 - CRC
        
        if (contentLength < 4) {
            throw new IllegalArgumentException("Heartbeat message content too short");
        }
        
        // 读取终端信息
        this.terminalInfo = buffer.readByte();
        parseTerminalInfo(this.terminalInfo);
        
        // 读取电压等级
        this.voltageLevel = buffer.readUnsignedByte();
        
        // 读取GSM信号强度
        this.gsmSignalStrength = buffer.readUnsignedByte();
        
        // 读取语言设置
        this.language = buffer.readByte();
        
        // 如果有额外内容，跳过
        if (contentLength > 4) {
            buffer.skipBytes(contentLength - 4);
        }
        
        // 读取包尾
        readPacketTail(buffer);
    }
    
    /**
     * 构建终端信息字节
     * 
     * @return 终端信息字节
     */
    private byte buildTerminalInfoByte() {
        int info = 0;
        
        if (oilElectricityDisconnected) info |= 0x01;  // bit 0
        if (gpsTracking) info |= 0x02;                 // bit 1
        if (charging) info |= 0x04;                    // bit 2
        if (accOn) info |= 0x08;                       // bit 3
        if (armed) info |= 0x10;                       // bit 4
        
        return (byte) info;
    }
    
    /**
     * 解析终端信息字节
     * 
     * @param terminalInfo 终端信息字节
     */
    private void parseTerminalInfo(byte terminalInfo) {
        this.oilElectricityDisconnected = (terminalInfo & 0x01) != 0;
        this.gpsTracking = (terminalInfo & 0x02) != 0;
        this.charging = (terminalInfo & 0x04) != 0;
        this.accOn = (terminalInfo & 0x08) != 0;
        this.armed = (terminalInfo & 0x10) != 0;
    }
    
    @Override
    public boolean validate() {
        // 验证协议号
        if (protocolNumber != GT06Constants.PROTOCOL_HEARTBEAT) {
            return false;
        }
        
        // 验证序列号
        if (serialNumber < 0 || serialNumber > 0xFFFF) {
            return false;
        }
        
        // 验证电压等级（通常在0-6之间）
        if (voltageLevel < 0 || voltageLevel > 6) {
            return false;
        }
        
        // 验证GSM信号强度（通常在0-4之间）
        if (gsmSignalStrength < 0 || gsmSignalStrength > 4) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public String getMessageDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("Heartbeat - ");
        desc.append("Voltage: ").append(getVoltageDescription());
        desc.append(", GSM: ").append(getGsmSignalDescription());
        desc.append(", Status: ");
        
        if (accOn) desc.append("ACC_ON ");
        if (charging) desc.append("CHARGING ");
        if (gpsTracking) desc.append("GPS_ON ");
        if (armed) desc.append("ARMED ");
        if (oilElectricityDisconnected) desc.append("OIL_CUT ");
        
        return desc.toString().trim();
    }
    
    @Override
    public GT06Message createResponse() {
        return ResponseMessage.createHeartbeatResponse(this.serialNumber);
    }
    
    /**
     * 获取电压等级描述
     * 
     * @return 电压等级描述
     */
    public String getVoltageDescription() {
        switch (voltageLevel) {
            case 0: return "No Power";
            case 1: return "Extremely Low";
            case 2: return "Very Low";
            case 3: return "Low";
            case 4: return "Medium";
            case 5: return "High";
            case 6: return "Highest";
            default: return "Unknown(" + voltageLevel + ")";
        }
    }
    
    /**
     * 获取GSM信号强度描述
     * 
     * @return GSM信号强度描述
     */
    public String getGsmSignalDescription() {
        switch (gsmSignalStrength) {
            case 0: return "No Signal";
            case 1: return "Very Weak";
            case 2: return "Weak";
            case 3: return "Good";
            case 4: return "Strong";
            default: return "Unknown(" + gsmSignalStrength + ")";
        }
    }
    
    /**
     * 获取语言设置描述
     * 
     * @return 语言设置描述
     */
    public String getLanguageDescription() {
        switch (language) {
            case 0x01: return "Chinese";
            case 0x02: return "English";
            default: return "Unknown(" + String.format("0x%02X", language & 0xFF) + ")";
        }
    }
    
    /**
     * 检查设备是否处于正常状态
     * 
     * @return 设备是否正常
     */
    public boolean isDeviceHealthy() {
        // 设备健康状态判断：
        // 1. 有GSM信号
        // 2. 电压不是极低状态
        // 3. GPS跟踪正常
        return gsmSignalStrength > 0 && voltageLevel > 1 && gpsTracking;
    }
    
    /**
     * 检查是否需要告警
     * 
     * @return 是否需要告警
     */
    public boolean needsAlert() {
        // 需要告警的情况：
        // 1. 电压极低
        // 2. 无GSM信号
        // 3. 油电被断开
        return voltageLevel <= 1 || gsmSignalStrength == 0 || oilElectricityDisconnected;
    }
}
