server:
  port: 8080
  servlet:
    context-path: /gt06
  tomcat:
    threads:
      max: 200
      min-spare: 10
    max-connections: 8192
    accept-count: 100

spring:
  application:
    name: gt06-system

  profiles:
    active: dev

  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************
    username: root
    password: yunqu168
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: GT06HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        cache:
          use_second_level_cache: false
          use_query_cache: false

  # Redisson配置
  redisson:
    config: |
      singleServerConfig:
        address: "redis://************:6379"
        database: 2
        connectionPoolSize: 10
        connectionMinimumIdleSize: 5
        idleConnectionTimeout: 10000
        connectTimeout: 10000
        timeout: 3000
        retryAttempts: 3
        retryInterval: 1500

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000
      cache-null-values: false
      use-key-prefix: true
      key-prefix: "gt06:"

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false

  # 任务调度配置
  task:
    execution:
      pool:
        core-size: 10
        max-size: 50
        queue-capacity: 200
        keep-alive: 60s
      thread-name-prefix: gt06-task-
    scheduling:
      pool:
        size: 5
      thread-name-prefix: gt06-scheduling-

# GT06网络层配置
gt06:
  network:
    server:
      port: 8888
      boss-threads: 1
      worker-threads: 4
      so-backlog: 1024
      so-keepalive: true
      tcp-nodelay: true
      auto-start: true
      so-reuseaddr: true
      connect-timeout: 30000
      read-timeout: 300
      write-timeout: 60
      all-timeout: 360
      enable-logging: true
      log-level: DEBUG

    # 会话管理配置
    session:
      max-idle-time: 300000
      cleanup-interval: 60000
      max-sessions: 10000

    # 消息处理配置
    message:
      max-frame-length: 1024
      length-field-offset: 2
      length-field-length: 1
      length-adjustment: 0
      initial-bytes-to-strip: 0

  # 业务配置
  business:
    # 设备配置
    device:
      auto-register: true
      max-offline-hours: 24
      service-expire-notify-days: 30

    # 位置数据配置
    location:
      data-retention-days: 365
      track-point-interval: 30
      geocoding-enabled: false

    # 报警配置
    alarm:
      data-retention-days: 180
      critical-notify-enabled: true
      timeout-minutes: 30
      auto-escalate-enabled: true

    # 统计配置
    statistics:
      cache-duration: 300
      daily-cleanup-hour: 2

# 日志配置
logging:
  level:
    root: INFO
    com.gt06: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/gt06-system.log
    max-size: 100MB
    max-history: 30

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
      sla:
        http.server.requests: 10ms,50ms,100ms,200ms,500ms

# Swagger文档配置
springdoc:
  api-docs:
    enabled: true
    path: /api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    operations-sorter: method
    tags-sorter: alpha
  packages-to-scan: com.gt06.web.controller

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev

  datasource:
    url: ************************************************************************************************************************************************************

  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update

logging:
  level:
    com.gt06: DEBUG
    org.springframework.web: DEBUG

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test

  datasource:
    url: *************************************************************************************************************************************************************

  jpa:
    hibernate:
      ddl-auto: create-drop

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod

  datasource:
    url: **************************************************************************************************************************
    username: ${DB_USERNAME:gt06_user}
    password: ${DB_PASSWORD:gt06_password}
    hikari:
      maximum-pool-size: 50

  redisson:
    config: |
      singleServerConfig:
        address: "redis://************:6379"
        password: yunqu168
        database: 0
        connectionPoolSize: 20
        connectionMinimumIdleSize: 10

  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate

logging:
  level:
    root: WARN
    com.gt06: INFO
  file:
    name: /var/log/gt06/gt06-system.log

gt06:
  network:
    server:
      worker-threads: 8
    session:
      max-sessions: 50000
