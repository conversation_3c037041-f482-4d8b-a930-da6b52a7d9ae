package com.gt06.web;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * GT06 Web应用程序启动类
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@SpringBootApplication(scanBasePackages = {
    "com.gt06.web",
    "com.gt06.service",
    "com.gt06.network",
    "com.gt06.common"
})
@EntityScan(basePackages = "com.gt06.service.entity")
@EnableJpaRepositories(basePackages = "com.gt06.service.repository")
@EnableCaching
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
public class GT06WebApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(GT06WebApplication.class, args);
    }
}
