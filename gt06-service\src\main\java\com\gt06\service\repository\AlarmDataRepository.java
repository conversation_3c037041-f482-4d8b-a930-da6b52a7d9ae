package com.gt06.service.repository;

import com.gt06.service.entity.AlarmData;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 报警数据访问接口
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Repository
public interface AlarmDataRepository extends JpaRepository<AlarmData, Long> {
    
    /**
     * 根据IMEI查找报警记录
     * 
     * @param imei 设备IMEI
     * @param pageable 分页参数
     * @return 报警记录分页列表
     */
    Page<AlarmData> findByImeiOrderByAlarmTimeDesc(String imei, Pageable pageable);
    
    /**
     * 根据IMEI和时间范围查找报警记录
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报警记录列表
     */
    List<AlarmData> findByImeiAndAlarmTimeBetweenOrderByAlarmTimeDesc(
        String imei, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据报警类型查找报警记录
     * 
     * @param alarmType 报警类型
     * @param pageable 分页参数
     * @return 报警记录分页列表
     */
    Page<AlarmData> findByAlarmTypeOrderByAlarmTimeDesc(AlarmData.AlarmType alarmType, Pageable pageable);
    
    /**
     * 根据报警级别查找报警记录
     * 
     * @param alarmLevel 报警级别
     * @param pageable 分页参数
     * @return 报警记录分页列表
     */
    Page<AlarmData> findByAlarmLevelOrderByAlarmTimeDesc(AlarmData.AlarmLevel alarmLevel, Pageable pageable);
    
    /**
     * 根据处理状态查找报警记录
     * 
     * @param status 处理状态
     * @param pageable 分页参数
     * @return 报警记录分页列表
     */
    Page<AlarmData> findByStatusOrderByAlarmTimeDesc(AlarmData.AlarmStatus status, Pageable pageable);
    
    /**
     * 查找待处理的报警记录
     * 
     * @param pageable 分页参数
     * @return 待处理报警记录分页列表
     */
    @Query("SELECT a FROM AlarmData a WHERE a.status = 'PENDING' ORDER BY a.alarmLevel DESC, a.alarmTime DESC")
    Page<AlarmData> findPendingAlarms(Pageable pageable);
    
    /**
     * 查找紧急报警记录
     * 
     * @param pageable 分页参数
     * @return 紧急报警记录分页列表
     */
    @Query("SELECT a FROM AlarmData a WHERE a.alarmLevel = 'CRITICAL' ORDER BY a.alarmTime DESC")
    Page<AlarmData> findCriticalAlarms(Pageable pageable);
    
    /**
     * 查找未通知的报警记录
     * 
     * @return 未通知报警记录列表
     */
    List<AlarmData> findByNotifiedFalseOrderByAlarmTimeDesc();
    
    /**
     * 根据多个条件查询报警记录
     * 
     * @param imei 设备IMEI
     * @param alarmType 报警类型
     * @param alarmLevel 报警级别
     * @param status 处理状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 报警记录分页列表
     */
    @Query("SELECT a FROM AlarmData a WHERE " +
           "(:imei IS NULL OR a.imei = :imei) AND " +
           "(:alarmType IS NULL OR a.alarmType = :alarmType) AND " +
           "(:alarmLevel IS NULL OR a.alarmLevel = :alarmLevel) AND " +
           "(:status IS NULL OR a.status = :status) AND " +
           "(:startTime IS NULL OR a.alarmTime >= :startTime) AND " +
           "(:endTime IS NULL OR a.alarmTime <= :endTime) " +
           "ORDER BY a.alarmTime DESC")
    Page<AlarmData> findAlarmsByConditions(@Param("imei") String imei,
                                          @Param("alarmType") AlarmData.AlarmType alarmType,
                                          @Param("alarmLevel") AlarmData.AlarmLevel alarmLevel,
                                          @Param("status") AlarmData.AlarmStatus status,
                                          @Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime,
                                          Pageable pageable);
    
    /**
     * 统计报警数量
     * 
     * @param imei 设备IMEI
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报警数量
     */
    long countByImeiAndAlarmTimeBetween(String imei, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据报警类型统计数量
     * 
     * @param alarmType 报警类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报警数量
     */
    long countByAlarmTypeAndAlarmTimeBetween(AlarmData.AlarmType alarmType, 
                                            LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据报警级别统计数量
     * 
     * @param alarmLevel 报警级别
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报警数量
     */
    long countByAlarmLevelAndAlarmTimeBetween(AlarmData.AlarmLevel alarmLevel, 
                                             LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计待处理报警数量
     * 
     * @return 待处理报警数量
     */
    long countByStatus(AlarmData.AlarmStatus status);
    
    /**
     * 统计紧急报警数量
     * 
     * @return 紧急报警数量
     */
    long countByAlarmLevel(AlarmData.AlarmLevel alarmLevel);
    
    /**
     * 批量更新报警状态
     * 
     * @param ids 报警ID列表
     * @param status 新状态
     * @param handler 处理人
     * @param handleTime 处理时间
     * @param remarks 处理备注
     * @return 更新数量
     */
    @Modifying
    @Query("UPDATE AlarmData a SET a.status = :status, a.handler = :handler, " +
           "a.handleTime = :handleTime, a.handleRemarks = :remarks " +
           "WHERE a.id IN :ids")
    int batchUpdateStatus(@Param("ids") List<Long> ids,
                         @Param("status") AlarmData.AlarmStatus status,
                         @Param("handler") String handler,
                         @Param("handleTime") LocalDateTime handleTime,
                         @Param("remarks") String remarks);
    
    /**
     * 批量标记已通知
     * 
     * @param ids 报警ID列表
     * @param notifyTime 通知时间
     * @param notifyMethod 通知方式
     * @return 更新数量
     */
    @Modifying
    @Query("UPDATE AlarmData a SET a.notified = true, a.notifyTime = :notifyTime, " +
           "a.notifyMethod = :notifyMethod WHERE a.id IN :ids")
    int batchMarkNotified(@Param("ids") List<Long> ids,
                         @Param("notifyTime") LocalDateTime notifyTime,
                         @Param("notifyMethod") String notifyMethod);
    
    /**
     * 删除过期的报警数据
     * 
     * @param expireTime 过期时间
     * @return 删除数量
     */
    @Modifying
    @Query("DELETE FROM AlarmData a WHERE a.createTime < :expireTime")
    int deleteExpiredAlarms(@Param("expireTime") LocalDateTime expireTime);
    
    /**
     * 统计报警类型分布
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @Query("SELECT a.alarmType, COUNT(a) FROM AlarmData a WHERE " +
           "a.alarmTime BETWEEN :startTime AND :endTime " +
           "GROUP BY a.alarmType ORDER BY COUNT(a) DESC")
    List<Object[]> getAlarmTypeDistribution(@Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计报警级别分布
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @Query("SELECT a.alarmLevel, COUNT(a) FROM AlarmData a WHERE " +
           "a.alarmTime BETWEEN :startTime AND :endTime " +
           "GROUP BY a.alarmLevel ORDER BY a.alarmLevel DESC")
    List<Object[]> getAlarmLevelDistribution(@Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计每日报警趋势
     * 
     * @param startTime 开始时间
     * @return 统计结果
     */
    @Query("SELECT DATE(a.alarmTime) as date, COUNT(a) as count " +
           "FROM AlarmData a WHERE a.alarmTime >= :startTime " +
           "GROUP BY DATE(a.alarmTime) ORDER BY DATE(a.alarmTime)")
    List<Object[]> getDailyAlarmTrend(@Param("startTime") LocalDateTime startTime);
    
    /**
     * 统计设备报警频率
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @Query("SELECT a.imei, COUNT(a) as count " +
           "FROM AlarmData a WHERE a.alarmTime BETWEEN :startTime AND :endTime " +
           "GROUP BY a.imei ORDER BY COUNT(a) DESC")
    List<Object[]> getDeviceAlarmFrequency(@Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找最近的报警记录
     * 
     * @param limit 限制数量
     * @return 最近报警记录列表
     */
    @Query("SELECT a FROM AlarmData a ORDER BY a.alarmTime DESC")
    List<AlarmData> findRecentAlarms(Pageable pageable);
    
    /**
     * 查找处理时间超时的报警
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 超时报警列表
     */
    @Query("SELECT a FROM AlarmData a WHERE " +
           "a.status = 'PENDING' AND " +
           "a.alarmTime <= :timeoutTime " +
           "ORDER BY a.alarmLevel DESC, a.alarmTime ASC")
    List<AlarmData> findTimeoutAlarms(@Param("timeoutTime") LocalDateTime timeoutTime);
    
    /**
     * 统计报警处理效率
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均处理时间（分钟）
     */
    @Query("SELECT AVG(TIMESTAMPDIFF(MINUTE, a.alarmTime, a.handleTime)) " +
           "FROM AlarmData a WHERE " +
           "a.handleTime IS NOT NULL AND " +
           "a.alarmTime BETWEEN :startTime AND :endTime")
    Double getAverageHandleTime(@Param("startTime") LocalDateTime startTime,
                               @Param("endTime") LocalDateTime endTime);
}
