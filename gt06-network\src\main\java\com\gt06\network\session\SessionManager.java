package com.gt06.network.session;

import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;

/**
 * 会话管理器
 * 负责管理设备连接会话的生命周期
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Component
public class SessionManager {
    
    /** 通道ID到会话的映射 */
    private final ConcurrentMap<String, DeviceSession> channelSessions = new ConcurrentHashMap<>();
    
    /** IMEI到会话的映射 */
    private final ConcurrentMap<String, DeviceSession> imeiSessions = new ConcurrentHashMap<>();
    
    /** 会话清理任务调度器 */
    private final ScheduledExecutorService cleanupScheduler = Executors.newSingleThreadScheduledExecutor(
        r -> new Thread(r, "session-cleanup"));
    
    /** 会话超时时间（分钟） */
    private static final int SESSION_TIMEOUT_MINUTES = 30;
    
    public SessionManager() {
        // 启动会话清理任务，每5分钟执行一次
        cleanupScheduler.scheduleAtFixedRate(this::cleanupExpiredSessions, 5, 5, TimeUnit.MINUTES);
    }
    
    /**
     * 创建新会话
     * 
     * @param channel 网络通道
     * @param clientIp 客户端IP
     * @param clientPort 客户端端口
     * @return 创建的会话
     */
    public DeviceSession createSession(Channel channel, String clientIp, int clientPort) {
        String channelId = channel.id().asShortText();
        
        DeviceSession session = new DeviceSession(channel, clientIp, clientPort);
        channelSessions.put(channelId, session);
        
        log.debug("Created session for channel: {} from {}:{}", channelId, clientIp, clientPort);
        
        return session;
    }
    
    /**
     * 移除会话
     * 
     * @param channel 网络通道
     */
    public void removeSession(Channel channel) {
        String channelId = channel.id().asShortText();
        DeviceSession session = channelSessions.remove(channelId);
        
        if (session != null) {
            // 如果会话已认证，也要从IMEI映射中移除
            if (session.isAuthenticated() && session.getImei() != null) {
                imeiSessions.remove(session.getImei());
                log.debug("Removed authenticated session for IMEI: {}", session.getImei());
            }
            
            log.debug("Removed session for channel: {}", channelId);
        }
    }
    
    /**
     * 根据通道获取会话
     * 
     * @param channel 网络通道
     * @return 会话对象，如果不存在返回null
     */
    public DeviceSession getSession(Channel channel) {
        String channelId = channel.id().asShortText();
        return channelSessions.get(channelId);
    }
    
    /**
     * 根据IMEI获取会话
     * 
     * @param imei 设备IMEI
     * @return 会话对象，如果不存在返回null
     */
    public DeviceSession getSessionByImei(String imei) {
        return imeiSessions.get(imei);
    }
    
    /**
     * 认证会话
     * 
     * @param channel 网络通道
     * @param imei 设备IMEI
     * @return 是否认证成功
     */
    public boolean authenticateSession(Channel channel, String imei) {
        DeviceSession session = getSession(channel);
        if (session == null) {
            log.warn("Cannot authenticate - session not found for channel: {}", channel.id().asShortText());
            return false;
        }
        
        // 检查是否已有相同IMEI的会话
        DeviceSession existingSession = imeiSessions.get(imei);
        if (existingSession != null && !existingSession.getChannel().equals(channel)) {
            log.warn("IMEI {} already authenticated on another channel, closing old session", imei);
            existingSession.getChannel().close();
            imeiSessions.remove(imei);
        }
        
        // 认证当前会话
        session.authenticate(imei);
        imeiSessions.put(imei, session);
        
        log.info("Session authenticated for IMEI: {} on channel: {}", imei, channel.id().asShortText());
        
        return true;
    }
    
    /**
     * 检查通道是否已认证
     * 
     * @param channel 网络通道
     * @return 是否已认证
     */
    public boolean isAuthenticated(Channel channel) {
        DeviceSession session = getSession(channel);
        return session != null && session.isAuthenticated();
    }
    
    /**
     * 更新会话的最后活动时间
     * 
     * @param channel 网络通道
     */
    public void updateLastActivity(Channel channel) {
        DeviceSession session = getSession(channel);
        if (session != null) {
            session.updateLastActivity();
        }
    }
    
    /**
     * 获取所有活跃会话
     * 
     * @return 活跃会话集合
     */
    public Collection<DeviceSession> getActiveSessions() {
        return channelSessions.values();
    }
    
    /**
     * 获取已认证的会话数量
     * 
     * @return 已认证会话数量
     */
    public int getAuthenticatedSessionCount() {
        return imeiSessions.size();
    }
    
    /**
     * 获取总会话数量
     * 
     * @return 总会话数量
     */
    public int getTotalSessionCount() {
        return channelSessions.size();
    }
    
    /**
     * 清理过期会话
     */
    private void cleanupExpiredSessions() {
        LocalDateTime expireTime = LocalDateTime.now().minusMinutes(SESSION_TIMEOUT_MINUTES);
        
        channelSessions.entrySet().removeIf(entry -> {
            DeviceSession session = entry.getValue();
            if (session.getLastActivity().isBefore(expireTime)) {
                log.info("Cleaning up expired session: {} (IMEI: {})", 
                    entry.getKey(), session.getImei());
                
                // 关闭通道
                if (session.getChannel().isActive()) {
                    session.getChannel().close();
                }
                
                // 从IMEI映射中移除
                if (session.getImei() != null) {
                    imeiSessions.remove(session.getImei());
                }
                
                return true;
            }
            return false;
        });
    }
    
    /**
     * 获取会话统计信息
     * 
     * @return 会话统计
     */
    public SessionStats getSessionStats() {
        int totalSessions = getTotalSessionCount();
        int authenticatedSessions = getAuthenticatedSessionCount();
        int unauthenticatedSessions = totalSessions - authenticatedSessions;
        
        return new SessionStats(totalSessions, authenticatedSessions, unauthenticatedSessions);
    }
    
    /**
     * 关闭所有会话
     */
    public void shutdown() {
        log.info("Shutting down session manager...");
        
        // 关闭所有活跃会话
        for (DeviceSession session : channelSessions.values()) {
            if (session.getChannel().isActive()) {
                session.getChannel().close();
            }
        }
        
        // 清空映射
        channelSessions.clear();
        imeiSessions.clear();
        
        // 关闭清理任务调度器
        cleanupScheduler.shutdown();
        
        log.info("Session manager shutdown completed");
    }
    
    /**
     * 会话统计信息
     */
    public static class SessionStats {
        private final int totalSessions;
        private final int authenticatedSessions;
        private final int unauthenticatedSessions;
        
        public SessionStats(int totalSessions, int authenticatedSessions, int unauthenticatedSessions) {
            this.totalSessions = totalSessions;
            this.authenticatedSessions = authenticatedSessions;
            this.unauthenticatedSessions = unauthenticatedSessions;
        }
        
        public int getTotalSessions() { return totalSessions; }
        public int getAuthenticatedSessions() { return authenticatedSessions; }
        public int getUnauthenticatedSessions() { return unauthenticatedSessions; }
        
        public double getAuthenticationRate() {
            return totalSessions > 0 ? (double) authenticatedSessions / totalSessions : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format("SessionStats{total=%d, authenticated=%d, unauthenticated=%d, authRate=%.2f%%}", 
                totalSessions, authenticatedSessions, unauthenticatedSessions, getAuthenticationRate() * 100);
        }
    }
}
