package com.gt06.protocol.message;

import com.gt06.common.constants.GT06Constants;
import com.gt06.common.util.ByteUtil;
import io.netty.buffer.ByteBuf;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * GT06协议位置数据消息 (0x12)
 * 设备向服务器发送的GPS/LBS位置信息
 * 
 * 消息格式：
 * 起始位(2) + 包长度(1) + 协议号(1) + 日期时间(6) + GPS信息长度(1) + GPS信息(N) + 
 * LBS信息长度(1) + LBS信息(N) + [里程信息(4)] + 序列号(2) + CRC(2) + 停止位(2)
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Getter
@Setter
public class LocationMessage extends GT06Message {
    
    /** GPS定位时间 */
    private LocalDateTime gpsDateTime;
    
    /** GPS卫星数量 */
    private int satelliteCount;
    
    /** 纬度（度） */
    private double latitude;
    
    /** 经度（度） */
    private double longitude;
    
    /** 速度（km/h） */
    private int speed;
    
    /** 方向角度（0-359度） */
    private int direction;
    
    /** 海拔高度（米） */
    private int altitude;
    
    /** GPS是否定位 */
    private boolean gpsFixed;
    
    /** ACC是否开启 */
    private boolean accOn;
    
    /** 是否为差分定位 */
    private boolean differentialGps;
    
    /** 移动国家代码 */
    private int mcc;
    
    /** 移动网络代码 */
    private int mnc;
    
    /** 位置区码 */
    private int lac;
    
    /** 基站ID */
    private int cellId;
    
    /** 信号强度 */
    private int signalStrength;
    
    /** 里程信息（可选，单位：米） */
    private Long mileage;
    
    /** 定位类型 */
    private LocationType locationType;
    
    /**
     * 定位类型枚举
     */
    public enum LocationType {
        GPS_ONLY,       // 仅GPS定位
        LBS_ONLY,       // 仅LBS定位
        GPS_LBS,        // GPS+LBS定位
        WIFI_LBS        // WIFI+LBS定位
    }
    
    /**
     * 构造函数
     */
    public LocationMessage() {
        super(GT06Constants.PROTOCOL_LOCATION);
    }
    
    @Override
    public byte[] encode() {
        // 编码日期时间（6字节）
        byte[] dateTimeBytes = ByteUtil.dateTimeToBytes(gpsDateTime);
        
        // 编码GPS信息
        byte[] gpsInfo = encodeGpsInfo();
        
        // 编码LBS信息
        byte[] lbsInfo = encodeLbsInfo();
        
        // 编码里程信息（可选）
        byte[] mileageBytes = encodeMileage();
        
        // 计算内容长度
        int contentLength = 6 + 1 + gpsInfo.length + 1 + lbsInfo.length + mileageBytes.length;
        
        // 创建包头
        byte[] header = createPacketHeader(contentLength);
        
        // 组装内容
        byte[] content = ByteUtil.concat(
            dateTimeBytes,
            new byte[]{(byte) gpsInfo.length},
            gpsInfo,
            new byte[]{(byte) lbsInfo.length},
            lbsInfo,
            mileageBytes
        );
        
        // 组装完整数据包（不包含CRC和停止位）
        byte[] dataForCRC = ByteUtil.concat(header, content);
        
        // 创建包尾
        byte[] tail = createPacketTail(dataForCRC);
        
        // 返回完整数据包
        return ByteUtil.concat(dataForCRC, tail);
    }
    
    /**
     * 编码GPS信息
     * 
     * @return GPS信息字节数组
     */
    private byte[] encodeGpsInfo() {
        if (!gpsFixed || latitude == 0 || longitude == 0) {
            return new byte[0]; // 无GPS信息
        }
        
        // GPS信息格式：纬度(4) + 经度(4) + 速度(1) + 方向+状态(2) + 海拔(2)
        byte[] gpsData = new byte[13];
        
        // 编码纬度（度 * 30000 / 180 * 60）
        int latValue = (int) (latitude * 30000.0 / 180.0 * 60.0);
        System.arraycopy(ByteUtil.intToBytes4(latValue), 0, gpsData, 0, 4);
        
        // 编码经度（度 * 30000 / 180 * 60）
        int lngValue = (int) (longitude * 30000.0 / 180.0 * 60);
        System.arraycopy(ByteUtil.intToBytes4(lngValue), 0, gpsData, 4, 4);
        
        // 编码速度
        gpsData[8] = (byte) speed;
        
        // 编码方向和状态信息
        int directionAndStatus = direction & 0x03FF; // 方向占10位
        if (gpsFixed) directionAndStatus |= 0x1000;
        if (differentialGps) directionAndStatus |= 0x2000;
        if (accOn) directionAndStatus |= 0x4000;
        
        System.arraycopy(ByteUtil.intToBytes2(directionAndStatus), 0, gpsData, 9, 2);
        
        // 编码海拔
        System.arraycopy(ByteUtil.intToBytes2(altitude), 0, gpsData, 11, 2);
        
        return gpsData;
    }
    
    /**
     * 编码LBS信息
     * 
     * @return LBS信息字节数组
     */
    private byte[] encodeLbsInfo() {
        if (mcc == 0 && mnc == 0 && lac == 0 && cellId == 0) {
            return new byte[0]; // 无LBS信息
        }
        
        // LBS信息格式：MCC(2) + MNC(1) + LAC(2) + CellID(3) + 信号强度(1)
        byte[] lbsData = new byte[9];
        
        System.arraycopy(ByteUtil.intToBytes2(mcc), 0, lbsData, 0, 2);
        lbsData[2] = (byte) mnc;
        System.arraycopy(ByteUtil.intToBytes2(lac), 0, lbsData, 3, 2);
        
        // CellID使用3字节
        byte[] cellIdBytes = ByteUtil.intToBytes4(cellId);
        System.arraycopy(cellIdBytes, 1, lbsData, 5, 3);
        
        lbsData[8] = (byte) signalStrength;
        
        return lbsData;
    }
    
    /**
     * 编码里程信息
     * 
     * @return 里程信息字节数组
     */
    private byte[] encodeMileage() {
        if (mileage == null) {
            return new byte[0];
        }
        
        return ByteUtil.intToBytes4(mileage.intValue());
    }
    
    @Override
    public void decode(ByteBuf buffer) {
        // 读取包头
        readPacketHeader(buffer);
        
        // 读取GPS时间（6字节）
        this.gpsDateTime = ByteUtil.bytesToDateTime(buffer.array(), buffer.readerIndex());
        buffer.skipBytes(6);
        
        // 读取GPS信息
        int gpsInfoLength = buffer.readUnsignedByte();
        if (gpsInfoLength > 0) {
            decodeGpsInfo(buffer, gpsInfoLength);
        }
        
        // 读取LBS信息
        int lbsInfoLength = buffer.readUnsignedByte();
        if (lbsInfoLength > 0) {
            decodeLbsInfo(buffer, lbsInfoLength);
        }
        
        // 检查是否有里程信息
        int remainingBytes = length - 1 - 6 - 1 - gpsInfoLength - 1 - lbsInfoLength - 2 - 2;
        if (remainingBytes >= 4) {
            this.mileage = (long) buffer.readInt();
        }
        
        // 读取包尾
        readPacketTail(buffer);
        
        // 确定定位类型
        determineLocationType();
    }
    
    /**
     * 解码GPS信息
     */
    private void decodeGpsInfo(ByteBuf buffer, int length) {
        if (length < 13) {
            buffer.skipBytes(length);
            return;
        }
        
        // 解码纬度
        int latValue = buffer.readInt();
        this.latitude = latValue * 180.0 / 30000.0 / 60.0;
        
        // 解码经度
        int lngValue = buffer.readInt();
        this.longitude = lngValue * 180.0 / 30000.0 / 60.0;
        
        // 解码速度
        this.speed = buffer.readUnsignedByte();
        
        // 解码方向和状态
        int directionAndStatus = buffer.readUnsignedShort();
        this.direction = directionAndStatus & 0x03FF;
        this.gpsFixed = (directionAndStatus & 0x1000) != 0;
        this.differentialGps = (directionAndStatus & 0x2000) != 0;
        this.accOn = (directionAndStatus & 0x4000) != 0;
        
        // 解码海拔
        this.altitude = buffer.readShort();
        
        // 跳过剩余字节
        if (length > 13) {
            buffer.skipBytes(length - 13);
        }
    }
    
    /**
     * 解码LBS信息
     */
    private void decodeLbsInfo(ByteBuf buffer, int length) {
        if (length < 9) {
            buffer.skipBytes(length);
            return;
        }
        
        this.mcc = buffer.readUnsignedShort();
        this.mnc = buffer.readUnsignedByte();
        this.lac = buffer.readUnsignedShort();
        this.cellId = ByteUtil.bytes3ToInt(buffer.array(), buffer.readerIndex());
        buffer.skipBytes(3);
        this.signalStrength = buffer.readUnsignedByte();
        
        // 跳过剩余字节
        if (length > 9) {
            buffer.skipBytes(length - 9);
        }
    }
    
    /**
     * 确定定位类型
     */
    private void determineLocationType() {
        boolean hasGps = gpsFixed && latitude != 0 && longitude != 0;
        boolean hasLbs = mcc != 0 || mnc != 0 || lac != 0 || cellId != 0;
        
        if (hasGps && hasLbs) {
            this.locationType = LocationType.GPS_LBS;
        } else if (hasGps) {
            this.locationType = LocationType.GPS_ONLY;
        } else if (hasLbs) {
            this.locationType = LocationType.LBS_ONLY;
        } else {
            this.locationType = null;
        }
    }
    
    @Override
    public boolean validate() {
        // 验证协议号
        if (protocolNumber != GT06Constants.PROTOCOL_LOCATION) {
            return false;
        }
        
        // 验证时间
        if (gpsDateTime == null) {
            return false;
        }
        
        // 验证坐标范围
        if (gpsFixed) {
            if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
                return false;
            }
        }
        
        // 验证速度和方向
        if (speed < 0 || speed > 255 || direction < 0 || direction > 359) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public String getMessageDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("Location - ");
        
        if (gpsFixed) {
            desc.append(String.format("GPS(%.6f,%.6f) ", latitude, longitude));
            desc.append(String.format("Speed:%dkm/h Dir:%d° ", speed, direction));
        }
        
        if (mcc != 0 || lac != 0) {
            desc.append(String.format("LBS(MCC:%d LAC:%d CellID:%d) ", mcc, lac, cellId));
        }
        
        if (accOn) desc.append("ACC_ON ");
        if (mileage != null) desc.append(String.format("Mileage:%dm ", mileage));
        
        return desc.toString().trim();
    }
    
    @Override
    public GT06Message createResponse() {
        return ResponseMessage.createLocationResponse(this.serialNumber);
    }
    
    /**
     * 检查是否为有效的GPS定位
     */
    public boolean isValidGpsLocation() {
        return gpsFixed && latitude != 0 && longitude != 0 && 
               latitude >= -90 && latitude <= 90 && 
               longitude >= -180 && longitude <= 180;
    }
    
    /**
     * 计算与另一个位置的距离（米）
     */
    public double distanceTo(LocationMessage other) {
        if (!isValidGpsLocation() || !other.isValidGpsLocation()) {
            return -1;
        }
        
        // 使用Haversine公式计算距离
        double lat1Rad = Math.toRadians(this.latitude);
        double lat2Rad = Math.toRadians(other.latitude);
        double deltaLatRad = Math.toRadians(other.latitude - this.latitude);
        double deltaLngRad = Math.toRadians(other.longitude - this.longitude);
        
        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return 6371000 * c; // 地球半径6371km
    }
}
