import java.util.Properties;
import org.hibernate.cfg.Configuration;

public class TestHibernateConfig {
    public static void main(String[] args) {
        try {
            Configuration config = new Configuration();
            
            // 测试基本配置
            Properties props = new Properties();
            props.setProperty("hibernate.dialect", "org.hibernate.dialect.MySQL8Dialect");
            props.setProperty("hibernate.cache.use_second_level_cache", "false");
            props.setProperty("hibernate.cache.use_query_cache", "false");
            
            config.setProperties(props);
            
            System.out.println("Hibernate配置测试成功！");
            System.out.println("二级缓存已禁用，应该不会出现JCacheRegionFactory错误");
            
        } catch (Exception e) {
            System.err.println("Hibernate配置测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
