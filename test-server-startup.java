import java.io.IOException;
import java.net.Socket;

/**
 * 简单的服务器启动测试
 */
public class TestServerStartup {
    public static void main(String[] args) {
        System.out.println("Testing GT06 TCP server startup...");
        
        // 等待几秒让服务器启动
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 尝试连接到服务器
        try (Socket socket = new Socket("localhost", 8888)) {
            System.out.println("✅ Successfully connected to GT06 TCP server on port 8888");
            System.out.println("Server is running!");
        } catch (IOException e) {
            System.out.println("❌ Failed to connect to GT06 TCP server: " + e.getMessage());
            System.out.println("Server may not be running or not listening on port 8888");
        }
    }
}
