package com.gt06.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.gt06.service.entity.Device;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 设备数据传输对象
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "设备信息")
public class DeviceDTO {
    
    @Schema(description = "设备ID", example = "1")
    private Long id;
    
    @NotBlank(message = "IMEI不能为空")
    @Pattern(regexp = "\\d{15}", message = "IMEI必须是15位数字")
    @Schema(description = "设备IMEI", example = "123456789012345", required = true)
    private String imei;
    
    @Size(max = 100, message = "设备名称长度不能超过100字符")
    @Schema(description = "设备名称", example = "测试设备001")
    private String deviceName;
    
    @Size(max = 50, message = "设备类型长度不能超过50字符")
    @Schema(description = "设备类型", example = "GT06N")
    private String deviceType;
    
    @Size(max = 50, message = "设备型号长度不能超过50字符")
    @Schema(description = "设备型号", example = "GT06N-V2.0")
    private String deviceModel;
    
    @Size(max = 50, message = "软件版本长度不能超过50字符")
    @Schema(description = "软件版本", example = "1.0.0")
    private String softwareVersion;
    
    @Size(max = 50, message = "硬件版本长度不能超过50字符")
    @Schema(description = "硬件版本", example = "2.0")
    private String hardwareVersion;
    
    @Schema(description = "设备状态")
    private Device.DeviceStatus status;
    
    @Schema(description = "是否在线", example = "true")
    private Boolean online;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后在线时间")
    private LocalDateTime lastOnlineTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后离线时间")
    private LocalDateTime lastOfflineTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "注册时间")
    private LocalDateTime registerTime;
    
    @Size(max = 20, message = "SIM卡号长度不能超过20字符")
    @Schema(description = "SIM卡号", example = "13800138000")
    private String simNumber;
    
    @Size(max = 100, message = "所有者长度不能超过100字符")
    @Schema(description = "设备所有者", example = "张三")
    private String owner;
    
    @Size(max = 20, message = "联系电话长度不能超过20字符")
    @Schema(description = "联系电话", example = "13800138000")
    private String contactPhone;
    
    @Size(max = 20, message = "车牌号长度不能超过20字符")
    @Schema(description = "车牌号", example = "京A12345")
    private String plateNumber;
    
    @Size(max = 50, message = "车辆类型长度不能超过50字符")
    @Schema(description = "车辆类型", example = "小型汽车")
    private String vehicleType;
    
    @Size(max = 50, message = "车辆品牌长度不能超过50字符")
    @Schema(description = "车辆品牌", example = "丰田")
    private String vehicleBrand;
    
    @Size(max = 20, message = "车辆颜色长度不能超过20字符")
    @Schema(description = "车辆颜色", example = "白色")
    private String vehicleColor;
    
    @Size(max = 100, message = "安装位置长度不能超过100字符")
    @Schema(description = "设备安装位置", example = "仪表台下方")
    private String installLocation;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "安装时间")
    private LocalDateTime installTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "服务到期时间")
    private LocalDateTime serviceExpireTime;
    
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;
    
    @Size(max = 500, message = "备注长度不能超过500字符")
    @Schema(description = "备注")
    private String remarks;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    
    // 扩展字段
    @Schema(description = "显示名称")
    private String displayName;
    
    @Schema(description = "在线时长（分钟）")
    private Long onlineDurationMinutes;
    
    @Schema(description = "离线时长（分钟）")
    private Long offlineDurationMinutes;
    
    @Schema(description = "是否服务过期")
    private Boolean serviceExpired;
    
    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        if (deviceName != null && !deviceName.trim().isEmpty()) {
            return deviceName;
        }
        if (plateNumber != null && !plateNumber.trim().isEmpty()) {
            return plateNumber;
        }
        return imei;
    }
    
    /**
     * 检查是否在线
     */
    public boolean isOnline() {
        return Boolean.TRUE.equals(online);
    }
    
    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(enabled);
    }
    
    /**
     * 检查服务是否过期
     */
    public boolean isServiceExpired() {
        return serviceExpireTime != null && serviceExpireTime.isBefore(LocalDateTime.now());
    }
}
