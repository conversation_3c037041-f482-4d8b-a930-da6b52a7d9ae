package com.gt06.service.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 设备实体类
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "gt06_device")
public class Device {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 设备IMEI，唯一标识 */
    @Column(name = "imei", length = 15, nullable = false, unique = true)
    private String imei;

    /** 设备名称 */
    @Column(name = "device_name", length = 100)
    private String deviceName;

    /** 设备类型 */
    @Column(name = "device_type", length = 50)
    private String deviceType;

    /** 设备型号 */
    @Column(name = "device_model", length = 50)
    private String deviceModel;

    /** 软件版本 */
    @Column(name = "software_version", length = 50)
    private String softwareVersion;

    /** 硬件版本 */
    @Column(name = "hardware_version", length = 50)
    private String hardwareVersion;

    /** 设备状态 */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20, nullable = false)
    private DeviceStatus status = DeviceStatus.INACTIVE;

    /** 是否在线 */
    @Column(name = "online", nullable = false)
    private Boolean online = false;

    /** 最后在线时间 */
    @Column(name = "last_online_time")
    private LocalDateTime lastOnlineTime;

    /** 最后离线时间 */
    @Column(name = "last_offline_time")
    private LocalDateTime lastOfflineTime;

    /** 注册时间 */
    @Column(name = "register_time")
    private LocalDateTime registerTime;

    /** SIM卡号 */
    @Column(name = "sim_number", length = 20)
    private String simNumber;

    /** 设备所有者 */
    @Column(name = "owner", length = 100)
    private String owner;

    /** 联系电话 */
    @Column(name = "contact_phone", length = 20)
    private String contactPhone;

    /** 车牌号 */
    @Column(name = "plate_number", length = 20)
    private String plateNumber;

    /** 车辆类型 */
    @Column(name = "vehicle_type", length = 50)
    private String vehicleType;

    /** 车辆品牌 */
    @Column(name = "vehicle_brand", length = 50)
    private String vehicleBrand;

    /** 车辆颜色 */
    @Column(name = "vehicle_color", length = 20)
    private String vehicleColor;

    /** 设备安装位置 */
    @Column(name = "install_location", length = 100)
    private String installLocation;

    /** 安装时间 */
    @Column(name = "install_time")
    private LocalDateTime installTime;

    /** 服务到期时间 */
    @Column(name = "service_expire_time")
    private LocalDateTime serviceExpireTime;

    /** 是否启用 */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /** 备注 */
    @Column(name = "remarks", length = 500)
    private String remarks;

    /** 创建时间 */
    @CreationTimestamp
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    /** 更新时间 */
    @UpdateTimestamp
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    /** 创建者 */
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /** 更新者 */
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    /**
     * 设备状态枚举
     */
    public enum DeviceStatus {
        /** 未激活 */
        INACTIVE("未激活"),
        /** 正常 */
        NORMAL("正常"),
        /** 报警 */
        ALARM("报警"),
        /** 离线 */
        OFFLINE("离线"),
        /** 休眠 */
        SLEEPING("休眠"),
        /** 故障 */
        FAULT("故障"),
        /** 停用 */
        DISABLED("停用");

        private final String description;

        DeviceStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查设备是否在线
     */
    public boolean isOnline() {
        return Boolean.TRUE.equals(online);
    }

    /**
     * 检查设备是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(enabled);
    }

    /**
     * 检查服务是否过期
     */
    public boolean isServiceExpired() {
        return serviceExpireTime != null && serviceExpireTime.isBefore(LocalDateTime.now());
    }

    /**
     * 获取设备显示名称
     */
    public String getDisplayName() {
        if (deviceName != null && !deviceName.trim().isEmpty()) {
            return deviceName;
        }
        if (plateNumber != null && !plateNumber.trim().isEmpty()) {
            return plateNumber;
        }
        return imei;
    }

    /**
     * 设置设备上线
     */
    public void setOnline() {
        this.online = true;
        this.lastOnlineTime = LocalDateTime.now();
        if (this.status == DeviceStatus.OFFLINE) {
            this.status = DeviceStatus.NORMAL;
        }
    }

    /**
     * 设置设备离线
     */
    public void setOffline() {
        this.online = false;
        this.lastOfflineTime = LocalDateTime.now();
        this.status = DeviceStatus.OFFLINE;
    }

    /**
     * 获取在线时长（分钟）
     */
    public long getOnlineDurationMinutes() {
        if (!isOnline() || lastOnlineTime == null) {
            return 0;
        }
        return java.time.Duration.between(lastOnlineTime, LocalDateTime.now()).toMinutes();
    }

    /**
     * 获取离线时长（分钟）
     */
    public long getOfflineDurationMinutes() {
        if (isOnline() || lastOfflineTime == null) {
            return 0;
        }
        return java.time.Duration.between(lastOfflineTime, LocalDateTime.now()).toMinutes();
    }
}
